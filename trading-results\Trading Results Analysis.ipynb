{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "from scipy.stats import skew, kurtosis, geom, rv_histogram, powerlaw, expon\n", "from datetime import timedelta"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["figsize = (4,3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Trades data analysis\n", "\n", "I am performing walk forward testing of a trading algorithm that I wrote. <br>\n", "The goal of this analysis is to draw some meaningfull conclusions about the performance of this account.\n", "\n", "<i> For the time being I decided not to share the dataset. </i>\n", "\n", "This is still a work in progress."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The data contains information on 92 trades performed by my trading algorithm."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["path = \"/home/<USER>/Desktop/OrdersReport.csv\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["raw = pd.read_csv(path, index_col=None, header=0, sep=\",\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["df = raw.copy()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The available columns are listed below:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 92 entries, 0 to 91\n", "Data columns (total 15 columns):\n", " #   Column          Non-Null Count  Dtype  \n", "---  ------          --------------  -----  \n", " 0   Ticket          92 non-null     int64  \n", " 1   Magic           92 non-null     int64  \n", " 2   Comment         92 non-null     object \n", " 3   Open Datetime   92 non-null     object \n", " 4   Open Price      92 non-null     float64\n", " 5   Close Datetime  92 non-null     object \n", " 6   Close Price     92 non-null     float64\n", " 7   Type            92 non-null     int64  \n", " 8   Lots            92 non-null     float64\n", " 9   Symbol          92 non-null     object \n", " 10  Take Profit     92 non-null     float64\n", " 11  Stop Loss       92 non-null     float64\n", " 12  Profit          92 non-null     float64\n", " 13  Swap            92 non-null     float64\n", " 14  Commission      92 non-null     int64  \n", "dtypes: float64(7), int64(4), object(4)\n", "memory usage: 10.9+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The names of columns are fairly self explanatory. <br>\n", "However I feel I should provide more information on these:\n", "* 'Ticket' is generated by the broker and is of no meaning. <br>\n", "* 'Magic' column is used to distinguish between trading algorithms. <br>\n", "* 'Comment' will be used to determine the reason why a trade was closed. <br>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data preparation\n", "\n", "### Data checks\n", "\n", "The data is generated by a script written in MQL4. <br>\n", "During the setup of the trading algorithms I made a mistake and entered the wrong 'magic' number into the algorithm. <br>\n", "To rectify this mistake I switch the magic number to the correct value."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["df.loc[df['Magic'] == 12345, 'Magic'] = 4444"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I expect that there are no missing values."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["Ticket            0\n", "Magic             0\n", "Comment           0\n", "Open Datetime     0\n", "Open Price        0\n", "Close Datetime    0\n", "Close Price       0\n", "Type              0\n", "Lots              0\n", "Symbol            0\n", "Take Profit       0\n", "Stop Loss         0\n", "Profit            0\n", "Swap              0\n", "Commission        0\n", "dtype: int64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isna().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I then check for constant columns. I excpect the 'Commission' column to be all zeroes."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["Ticket         False\n", "Magic          False\n", "Open Price     False\n", "Close Price    False\n", "Type           False\n", "Lots           False\n", "Take Profit    False\n", "Stop Loss      False\n", "Profit         False\n", "Swap           F<PERSON>e\n", "Commission      True\n", "dtype: bool"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df.var() == 0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Indeed, the commision columns is all zeroes."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Commission'][0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I expect that all open prices are positive, so I count how many are negative."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df['Open Price'] <= 0]['Open Price'].count()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The same should apply to close prices."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df['Close Price'] <= 0]['Close Price'].count()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Lots also should be positive."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df['Lots'] <= 0]['Lots'].count()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Transformations\n", "\n", "For aesthetic reasons, I strip the names of symbols from \"+\" and \".\"."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["df['Symbol'] = df['Symbol'].apply(lambda x: x.replace(\"+\", \"\"))\n", "df['Symbol'] = df['Symbol'].apply(lambda x: x.replace(\".\", \"\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Convert columns with timestamps from strings to datetimes."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["df['Open Datetime'] = pd.to_datetime(df['Open Datetime'])\n", "df['Close Datetime'] = pd.to_datetime(df['Close Datetime'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Chronological order is of utmost importance since I shall be performing running calculations later."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["df = df.sort_values('Open Datetime')\n", "df = df.reset_index(drop=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### New columns\n", "\n", "In this section I add new columns to the dataset.<br>\n", "I shall definitely want to perform aggregations by time so I add the necessary columns. <br> \n", "I start with extracting the date, hour and day of week from the \"Open Datetime\" column."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["df['Open Date'] = df['Open Datetime'].dt.date\n", "df['Open Hour'] = df['Open Datetime'].dt.hour    \n", "df['Open Day'] = df['Open Datetime'].dt.day_name()\n", "df['Open Datetime Seconds'] = pd.to_datetime(df['Open Datetime'], origin='unix').astype('int') "]}, {"cell_type": "markdown", "metadata": {}, "source": ["I extract the same information as above for the \"Close Datetime\" column."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["df['Close Date'] = df['Close Datetime'].dt.date\n", "df['Close Hour'] = df['Close Datetime'].dt.hour    \n", "df['Close Day'] = df['Close Datetime'].dt.day_name()\n", "df['Close Datetime Seconds'] = pd.to_datetime(df['Close Datetime'], origin='unix').astype('int')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I calculate the duration of individual trades. The final value is in minutes."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["df['Duration'] = (df['Close Datetime'] - df['Open Datetime'])\n", "df['Duration'] = df['Duration'].apply(lambda x: x.total_seconds() / 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I create a new column that is equall to the net profit from a trade."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["df['Profit'] = df['Profit'] + df['Swap'] + df['Commission']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I need a 'Profit Per Lot' column because different trades have different lot sizes."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["df['Profit Per Lot'] = df['Profit'] / ( df['Lots'] / 0.01)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I add two columns to describe the direction of a trade."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["df['Order Type'] = df['Type'].apply(lambda x: \"Buy\" if x == 0 else \"Sell\")\n", "df['Direction'] = df['Type'].apply(lambda x: 1 if x == 0 else -1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I extract the reason why a trade was closed from the 'Comment' column. The reason for closure is added by my broker to the end of my comment:"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["'srl-1.03-242[[sl]]'"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Comment'][0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In order to extract the reason for closure I search for specific strings in the comment."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["df['Stop Loss Hit'] = df['Comment'].apply(lambda x: 1 if \"[[sl]]\" in x else 0)\n", "df['Take Profit Hit'] = df['Comment'].apply(lambda x: 1 if \"[[tp]]\" in x else 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I calculate the percentage difference between the take profit and the stop loss columns. I define a helper function first:"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["def stops_dist(x):\n", "    \n", "    if x[2] == 1:                \n", "        return (x[0] / x[1] - 1) * 100    \n", "    else:    \n", "        return (x[1] / x[0] - 1) * 100"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It is then applied to a subset of the original data."]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["df['Stops Distance'] = df[['Take Profit', 'Stop Loss', 'Direction']].apply(stops_dist, axis=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Useless columns\n", "\n", "I delete unwanted columns:\n", "* Ticket - Generated by the broker for their internal purposes.\n", "* Commission - Constant column equall to zero.\n", "* Comment - Information was extracted.\n", "* Type - Information was extracted.\n", "* Take Profit - Information was extracted and is not relevant to this analysis.\n", "* Stop Loss - Information was extracted and is not relevant to this analysis."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["unwanted = ['Ticket', \n", "            'Commission',             \n", "            'Comment', \n", "            'Type', \n", "            'Take Profit', \n", "            'Stop Loss']\n", "\n", "df.drop(unwanted, axis=1, inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The shape of the original data was:"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["(92, 15)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["raw.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After cleaning the shape is:"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["(92, 24)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The clean data looks like this:"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Magic</th>\n", "      <th>Open Datetime</th>\n", "      <th>Open Price</th>\n", "      <th>Close Datetime</th>\n", "      <th>Close Price</th>\n", "      <th>Lots</th>\n", "      <th>Symbol</th>\n", "      <th>Profit</th>\n", "      <th>Swap</th>\n", "      <th>Open Date</th>\n", "      <th>...</th>\n", "      <th>Close Hour</th>\n", "      <th>Close Day</th>\n", "      <th>Close Datetime Seconds</th>\n", "      <th>Duration</th>\n", "      <th>Profit Per Lot</th>\n", "      <th>Order Type</th>\n", "      <th>Direction</th>\n", "      <th>Stop Loss Hit</th>\n", "      <th>Take Profit Hit</th>\n", "      <th>Stops Distance</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4444</td>\n", "      <td>2020-05-19 11:31:00</td>\n", "      <td>1.09543</td>\n", "      <td>2020-05-19 11:39:00</td>\n", "      <td>1.09538</td>\n", "      <td>0.01</td>\n", "      <td>EURUSD</td>\n", "      <td>0.21</td>\n", "      <td>0.0</td>\n", "      <td>2020-05-19</td>\n", "      <td>...</td>\n", "      <td>11</td>\n", "      <td>Tuesday</td>\n", "      <td>1589888340000000000</td>\n", "      <td>8.0</td>\n", "      <td>0.21</td>\n", "      <td>Sell</td>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.457634</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4444</td>\n", "      <td>2020-05-19 12:17:00</td>\n", "      <td>1.09548</td>\n", "      <td>2020-05-19 12:23:00</td>\n", "      <td>1.09539</td>\n", "      <td>0.01</td>\n", "      <td>EURUSD</td>\n", "      <td>0.38</td>\n", "      <td>0.0</td>\n", "      <td>2020-05-19</td>\n", "      <td>...</td>\n", "      <td>12</td>\n", "      <td>Tuesday</td>\n", "      <td>1589890980000000000</td>\n", "      <td>6.0</td>\n", "      <td>0.38</td>\n", "      <td>Sell</td>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.452107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4444</td>\n", "      <td>2020-05-19 17:40:00</td>\n", "      <td>1.09250</td>\n", "      <td>2020-05-19 21:59:00</td>\n", "      <td>1.09257</td>\n", "      <td>0.01</td>\n", "      <td>EURUSD</td>\n", "      <td>0.30</td>\n", "      <td>0.0</td>\n", "      <td>2020-05-19</td>\n", "      <td>...</td>\n", "      <td>21</td>\n", "      <td>Tuesday</td>\n", "      <td>1589925540000000000</td>\n", "      <td>259.0</td>\n", "      <td>0.30</td>\n", "      <td>Buy</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.908866</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4444</td>\n", "      <td>2020-05-20 13:03:00</td>\n", "      <td>1.09569</td>\n", "      <td>2020-05-20 13:20:00</td>\n", "      <td>1.09557</td>\n", "      <td>0.01</td>\n", "      <td>EURUSD</td>\n", "      <td>0.50</td>\n", "      <td>0.0</td>\n", "      <td>2020-05-20</td>\n", "      <td>...</td>\n", "      <td>13</td>\n", "      <td>Wednesday</td>\n", "      <td>1589980800000000000</td>\n", "      <td>17.0</td>\n", "      <td>0.50</td>\n", "      <td>Sell</td>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.452949</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4444</td>\n", "      <td>2020-05-20 13:57:00</td>\n", "      <td>1.09630</td>\n", "      <td>2020-05-20 14:29:00</td>\n", "      <td>1.09628</td>\n", "      <td>0.01</td>\n", "      <td>EURUSD</td>\n", "      <td>0.09</td>\n", "      <td>0.0</td>\n", "      <td>2020-05-20</td>\n", "      <td>...</td>\n", "      <td>14</td>\n", "      <td>Wednesday</td>\n", "      <td>1589984940000000000</td>\n", "      <td>32.0</td>\n", "      <td>0.09</td>\n", "      <td>Sell</td>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.457257</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["   Magic       Open Datetime  Open Price      Close Datetime  Close Price  \\\n", "0   4444 2020-05-19 11:31:00     1.09543 2020-05-19 11:39:00      1.09538   \n", "1   4444 2020-05-19 12:17:00     1.09548 2020-05-19 12:23:00      1.09539   \n", "2   4444 2020-05-19 17:40:00     1.09250 2020-05-19 21:59:00      1.09257   \n", "3   4444 2020-05-20 13:03:00     1.09569 2020-05-20 13:20:00      1.09557   \n", "4   4444 2020-05-20 13:57:00     1.09630 2020-05-20 14:29:00      1.09628   \n", "\n", "   Lots  Symbol  Profit  Swap   Open Date  ...  Close Hour  Close Day  \\\n", "0  0.01  EURUSD    0.21   0.0  2020-05-19  ...          11    Tuesday   \n", "1  0.01  EURUSD    0.38   0.0  2020-05-19  ...          12    Tuesday   \n", "2  0.01  EURUSD    0.30   0.0  2020-05-19  ...          21    Tuesday   \n", "3  0.01  EURUSD    0.50   0.0  2020-05-20  ...          13  Wednesday   \n", "4  0.01  EURUSD    0.09   0.0  2020-05-20  ...          14  Wednesday   \n", "\n", "   Close Datetime Seconds Duration  Profit Per Lot Order Type  Direction  \\\n", "0     1589888340000000000      8.0            0.21       Sell         -1   \n", "1     1589890980000000000      6.0            0.38       Sell         -1   \n", "2     1589925540000000000    259.0            0.30        Buy          1   \n", "3     1589980800000000000     17.0            0.50       Sell         -1   \n", "4     1589984940000000000     32.0            0.09       Sell         -1   \n", "\n", "   Stop Loss Hit  Take Profit Hit Stops Distance  \n", "0              1                0       0.457634  \n", "1              1                0       0.452107  \n", "2              1                0       0.908866  \n", "3              1                0       0.452949  \n", "4              1                0       0.457257  \n", "\n", "[5 rows x 24 columns]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The basics\n", "\n", "I'm going to start the analysis by computing basic statistics the should interest any algorithmic trader."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Trading started on:"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2020-05-19'"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["start = df['Open Date'].min()\n", "str(start)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The last trade in the data was closed on:"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2020-06-16'"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["stop = df['Close Date'].max()\n", "str(stop)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["That means the algorithms traded for nearly a month."]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["'28 days, 0:00:00'"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["str(stop - start)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Trades over the period in question:"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["92"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Profit\n", "\n", "#### Total profit\n", "\n", "Over the period in question the algorithms achieved a profit of:"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["118.02"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(df['Profit'].sum(), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Total profit by market\n", "\n", "As can be seen the bulk of the profit comes from 'USDCHF'."]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["df_symbol = df[['Symbol', 'Profit']].groupby(['Symbol'], as_index=False).sum()\n", "\n", "plt.figure(figsize=figsize)\n", "plt.bar(df_symbol['Symbol'], df_symbol['Profit'])\n", "plt.xticks(df_symbol['Symbol'], rotation=90)\n", "plt.ylabel('Profit (zł)')\n", "plt.xlabel('Symbol')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Total profit by market and trade direction\n", "The most money was made by shorting 'USDCHF'. The most money was lost buying 'US500'."]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Symbol</th>\n", "      <th>Order Type</th>\n", "      <th>Number of trades</th>\n", "      <th>Profit</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>USDCHF</td>\n", "      <td>Sell</td>\n", "      <td>10</td>\n", "      <td>180.18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>EURUSD</td>\n", "      <td>Buy</td>\n", "      <td>12</td>\n", "      <td>82.49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GBPUSD</td>\n", "      <td>Buy</td>\n", "      <td>14</td>\n", "      <td>39.59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>US500</td>\n", "      <td>Sell</td>\n", "      <td>5</td>\n", "      <td>3.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>USDJPY</td>\n", "      <td>Buy</td>\n", "      <td>1</td>\n", "      <td>-7.89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>USDCHF</td>\n", "      <td>Buy</td>\n", "      <td>1</td>\n", "      <td>-8.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>OILWTI</td>\n", "      <td>Sell</td>\n", "      <td>4</td>\n", "      <td>-12.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>GBPUSD</td>\n", "      <td>Sell</td>\n", "      <td>21</td>\n", "      <td>-33.66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>EURUSD</td>\n", "      <td>Sell</td>\n", "      <td>20</td>\n", "      <td>-49.54</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>US500</td>\n", "      <td>Buy</td>\n", "      <td>4</td>\n", "      <td>-76.74</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Symbol Order Type  Number of trades  Profit\n", "8  USDCHF       Sell                10  180.18\n", "0  EURUSD        Buy                12   82.49\n", "2  GBPUSD        Buy                14   39.59\n", "6   US500       Sell                 5    3.86\n", "9  USDJPY        Buy                 1   -7.89\n", "7  USDCHF        Buy                 1   -8.20\n", "4  OILWTI       Sell                 4  -12.07\n", "3  GBPUSD       Sell                21  -33.66\n", "1  EURUSD       Sell                20  -49.54\n", "5   US500        Buy                 4  -76.74"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["df_mkt = df[['Symbol', 'Order Type', 'Direction', 'Profit']]\n", "df_mkt = df_mkt.groupby(['Symbol', 'Order Type'], as_index=False)\n", "df_mkt = df_mkt.agg({\"Direction\": [np.sum], \"Profit\": [np.sum]})\n", "df_mkt.columns = df_mkt.columns.droplevel(1)\n", "df_mkt['Direction'] = np.abs(df_mkt['Direction'])\n", "df_mkt = df_mkt.rename(columns={\"Direction\" : \"Number of trades\"})\n", "df_mkt.sort_values('Profit', ascending=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Best days\n", "The 3 best days were:"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Close Date</th>\n", "      <th>Profit</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2020-06-15</td>\n", "      <td>139.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2020-06-11</td>\n", "      <td>115.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2020-06-05</td>\n", "      <td>94.04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Close Date  Profit\n", "18  2020-06-15  139.11\n", "16  2020-06-11  115.00\n", "12  2020-06-05   94.04"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["df_cdate = df[['Close Date', 'Profit']].groupby(['Close Date'], as_index=False).sum()\n", "df_cdate.sort_values('Profit', ascending=False).head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Worst days\n", "The worst 3 days were:"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Close Date</th>\n", "      <th>Profit</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2020-06-12</td>\n", "      <td>-87.14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2020-06-08</td>\n", "      <td>-64.77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-05-26</td>\n", "      <td>-32.51</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Close Date  Profit\n", "17  2020-06-12  -87.14\n", "13  2020-06-08  -64.77\n", "4   2020-05-26  -32.51"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["df_cdate.sort_values('Profit', ascending=True).head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Costs\n", "\n", "Amount of swap paid:"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["-7.6"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["np.sum(df['Swap'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Profit Per Lot\n", "Since the trades vary in lot size it makes more sense to look at 'Profit Per Lot' than at 'Profit'."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Profit Per Lot histogram\n", "As can be seen the distribution of 'Profit Per Lot' is nothing like a normal distribution:"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["width = 400\n", "height = 400\n", "dpi = 100\n", "\n", "plt.figure(figsize=(width/dpi, height/dpi))\n", "plt.hist(df['Profit Per Lot'])\n", "plt.ylabel('Frequency')\n", "plt.xlabel('Profit Per Lot (zł)')\n", "plt.title('Profit Per Lot histogram', fontsize=18)\n", "plt.tight_layout()\n", "plt.savefig('./img/profit_histogram.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The mean of the distribution is:"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.49"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(np.mean(df['Profit Per Lot']), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["However the median is negative so the most frequent trades were small losses:"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["-7.5"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(np.quantile(df['Profit Per Lot'], 0.5), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Standard deviation is:"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["22.85"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(np.std(df['Profit Per Lot']), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Interquartile range:"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["14.0"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(np.quantile(df['Profit Per Lot'], 0.75) - np.quantile(df['Profit Per Lot'], 0.25))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The distribution exhibits positive skew of:"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["3.06"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(skew(df['Profit Per Lot']), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The distribution is also leptokurtic: "]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["15.45"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(kurtosis(df['Profit Per Lot']), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Comment\n", "This is to be expected given the design of the algorithms. They have a predifined maximum loss per trade (they will take small losses more frequently) but can hold winning trades for up to a week (hence the big wins)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Profit Per Lot by symbol\n", "Given the presence of outliers I think it is apropriate that for the rest of this section I analyze the median of 'Profit Per Lot'.<br>\n", "As can be seen by far the worst market was 'US500'."]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["df_ppl = df[['Symbol', 'Profit Per Lot']].groupby(['Symbol'], as_index=False).quantile(0.5)\n", "df_ppl['Symbol'] = df_ppl['Symbol'].astype('str')\n", "\n", "plt.figure(figsize=figsize)\n", "plt.bar(df_ppl['Symbol'], df_ppl['Profit Per Lot'])\n", "plt.xticks(df_ppl['Symbol'], rotation=90)\n", "plt.ylabel('Median Profit Per Lot (zł)')\n", "plt.xlabel('Symbol')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Profit Per Lot by day of week\n", "As can be seen no particular day of the week is better for trading."]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["df_day = df[['Open Day', 'Profit Per Lot']].groupby(['Open Day'], as_index=False).quantile(0.5)\n", "df_day.rename(columns={\"Open Day\": \"Day Of Week\"}, inplace=True)\n", "\n", "plt.figure(figsize=figsize)\n", "plt.bar(df_day['Day Of Week'], df_day['Profit Per Lot'])\n", "plt.xticks(df_day['Day Of Week'], rotation=90)\n", "plt.ylabel('Median Profit Per Lot (zł)')\n", "plt.xlabel('Day Of Week')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Profit Per Lot by open hour\n", "The median profit per lot is positive for 2pm and 4pm. \n", "This is very interesting because:\n", "* Around 2pm typically macroeconomic news is released.\n", "* The european session closes at 5pm."]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["df_hour = df[['Open Hour', 'Profit Per Lot']].groupby(['Open Hour'], as_index=False).quantile(0.5)\n", "df_hour.rename(columns={\"Open Hour\": \"Hour Of Day\"}, inplace=True)\n", "\n", "plt.figure(figsize=figsize)\n", "plt.bar(df_hour['Hour Of Day'], df_hour['Profit Per Lot'])\n", "plt.ylabel('Median Profit Per Lot (zł)')\n", "plt.xlabel('Hour Of Day')\n", "\n", "ax = plt.gca()\n", "ax.xaxis.set_major_locator(plt.MaxNLocator(integer=True))        \n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Trades\n", "\n", "#### Best trades\n", "\n", "Here is a table with the 3 best trades."]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Symbol</th>\n", "      <th>Profit Per Lot</th>\n", "      <th>Profit</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>US500</td>\n", "      <td>140.300</td>\n", "      <td>140.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>USDCHF</td>\n", "      <td>87.545</td>\n", "      <td>175.09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>EURUSD</td>\n", "      <td>46.620</td>\n", "      <td>46.62</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Symbol  Profit Per Lot  Profit\n", "79   US500         140.300  140.30\n", "49  USDCHF          87.545  175.09\n", "44  EURUSD          46.620   46.62"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["df[['Symbol', 'Profit Per Lot', 'Profit']].sort_values('Profit Per Lot', ascending=False).head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Worst trades\n", "\n", "Here is a table with the 3 worst trades."]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Symbol</th>\n", "      <th>Profit Per Lot</th>\n", "      <th>Profit</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>US500</td>\n", "      <td>-41.10</td>\n", "      <td>-41.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>US500</td>\n", "      <td>-39.29</td>\n", "      <td>-39.29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>US500</td>\n", "      <td>-38.87</td>\n", "      <td>-38.87</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Symbol  Profit Per Lot  Profit\n", "68  US500          -41.10  -41.10\n", "74  US500          -39.29  -39.29\n", "77  US500          -38.87  -38.87"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["df[['Symbol', 'Profit Per Lot', 'Profit']].sort_values('Profit Per Lot', ascending=True).head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Average win\n", "\n", "A profitable trade on average nets:"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/plain": ["17.34"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["avg_profit = np.round(df[df['Profit Per Lot'] >= 0]['Profit Per Lot'].mean(), 2)\n", "avg_profit"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Average loss\n", "A unprofitable trade on average loses:"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["-10.84"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["avg_loss = np.round(df[df['Profit Per Lot'] < 0]['Profit Per Lot'].mean(), 2)\n", "avg_loss"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Profit / loss ratio"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.6"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(np.abs(avg_profit / avg_loss), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Percent of winning trades\n", "\n", "As can be seen winning trades occur 40% of the time."]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["40.22"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["win_prob = np.round(df[df['Profit Per Lot'] >= 0]['Profit Per Lot'].count() / df.shape[0] * 100, 2)\n", "win_prob"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Percent of losing trades\n", "Losing trades occur 60% of the time."]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"text/plain": ["59.78"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["loss_prob = np.round(df[df['Profit Per Lot'] < 0]['Profit Per Lot'].count() / df.shape[0] * 100, 2)\n", "loss_prob"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Probability of observing consecutive losses\n", "I use the well known formula to compute the probability of a series of 10 losses happening. This calculation should be taken with a grain of salt because the probabilities are most surely not fixed."]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Losses</th>\n", "      <th>Probability</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>0.36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>0.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>0.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>0.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td>0.03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8</td>\n", "      <td>0.02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>9</td>\n", "      <td>0.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10</td>\n", "      <td>0.01</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Losses  Probability\n", "0       1         0.60\n", "1       2         0.36\n", "2       3         0.21\n", "3       4         0.13\n", "4       5         0.08\n", "5       6         0.05\n", "6       7         0.03\n", "7       8         0.02\n", "8       9         0.01\n", "9      10         0.01"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["X = geom(loss_prob / 100)\n", "df_geom = pd.DataFrame([np.arange(1,11), np.round(np.power(loss_prob/100, np.arange(1,11)),2)]).T\n", "df_geom.columns = ['Losses', 'Probability']\n", "df_geom['Losses'] = df_geom['Losses'].astype('int')\n", "df_geom"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Trade duration\n", "\n", "Next I inspect the histogram of the duration of individual trades. <br>"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=figsize)\n", "plt.hist(df['Duration'])\n", "plt.ylabel('Frequency')\n", "plt.xlabel('Time (min)')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I wonder what distribution does this follow ? <br>\n", "I create a random discrete random variable from binned data, and fit both a exponential and power law distribution."]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["dur = df['Duration'].to_numpy()\n", "\n", "h = np.histogram(dur, bins=1000)\n", "D_emp = rv_histogram(h)\n", "\n", "a, loc, scale = powerlaw.fit(dur)\n", "D_power = powerlaw(a=a, loc=loc, scale=scale)\n", "\n", "loc, scale = expon.fit(dur)\n", "D_exp = expon(loc=loc, scale=scale)\n", "\n", "X = np.linspace(np.min(dur), np.max(dur), 1000)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Unfortunately, as can bee seen the cdf's do not match, but they could serve as a approximation if need be."]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=figsize)\n", "plt.plot(X, D_power.cdf(X), label=\"Power\")\n", "plt.plot(X, D_exp.cdf(X), label=\"Exponential\")\n", "plt.plot(X, D_emp.cdf(X), label=\"Empirical\")         \n", "plt.legend(loc='best')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["df_dur = df['Duration'].to_frame()\n", "df_dur['Duration'] = df_dur['Duration'].apply(lambda x: <PERSON><PERSON>ta(minutes=x))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Longest trades\n", "The longest trade took almost 4 days."]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Duration</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>3 days 18:01:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>3 days 09:33:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>3 days 08:06:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Duration\n", "8  3 days 18:01:00\n", "49 3 days 09:33:00\n", "9  3 days 08:06:00"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["df_dur = df_dur.sort_values('Duration', ascending=False)\n", "df_dur.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Shortest trades\n", "The shortest trade took 2 minutes."]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Duration</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>00:02:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>00:02:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>00:03:00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Duration\n", "31 00:02:00\n", "30 00:02:00\n", "65 00:03:00"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["df_dur = df_dur.sort_values('Duration', ascending=True)\n", "df_dur.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Trade duration vs Profit Per Lot\n", "\n", "The scatter plot shows that there seems to be a minimal relationship between the duration of a trade and its profitability. This is to be expected - as mentioned earlier the algorithm will hold on to winning trades."]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=figsize)\n", "plt.scatter(df['Profit Per Lot'], df['Duration'] / 60, s=3)\n", "plt.ylabel(\"Duration (min)\")\n", "plt.xlabel(\"Profit Per Lot (zł)\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Average number of Trades Per Day"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["df_open = df[['Open Date', 'Profit']]\n", "df_open = df_open.groupby(['Open Date'], as_index=False)\n", "df_open = df_open.count()\n", "df_open = df_open.rename(columns={'Profit': 'Trades Per Day'})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The average number of trades per day is:"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["4.84"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(df_open['Trades Per Day'].mean(), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Trades Per Day histogram"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAQgAAADQCAYAAAD77P8JAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAONklEQVR4nO3df5BdZX3H8feHJAzht21WawnrQqUgBRRcKDVFBbHFRlEKLVBhBuoQp5UWxBkIDLX0hxUcqihDrQEUEAQEAkMBa2EkOFDkRxJ+B4qVgPywRDsKREoEP/3jnsiKeTZnN/fZe3Pzec3szDnn3vM83zvZfPac55zzXNkmImJNNup1ARHRvxIQEVGUgIiIogRERBQlICKiKAEREUXTe13AWLNmzfLIyEivy4jY4CxevPiHtodeu72vAmJkZIS7776712VEbHAkPb6m7TnFiIiiBEREFFUPCEnTJC2VdF3tviKiu6biCOI4YNkU9BMRXVY1ICTNBuYC59XsJyLqqH0V4yzgRGCL0hskzQPmAQwPD1cuJ2oZmX99V9tbfvrcrrYXk1PtCELS+4FnbS8e7322F9getT06NPQrl2EjoodqnmLMAQ6UtBy4DNhP0sUV+4uILqsWELZPtj3b9ghwGPAt20fU6i8iui/3QURE0ZTcam17EbBoKvqKiO7JEUREFCUgIqIoARERRQmIiChKQEREUQIiIooSEBFRlICIiKIEREQUJSAioigBERFFCYiIKEpARERRAiIiihIQEVGUgIiIogRERBQlICKiKAEREUUJiIgoSkBERFECIiKKEhARUZSAiIiiBEREFCUgIqIoARERRQmIiChKQEREUQIiIoqqBYSkTSTdKeleSQ9K+rtafUVEHdMrtv0SsJ/tFyTNAG6V9A3b36nYZ0R0UbWAsG3ghWZ1RvPjWv1FRPfVPIJA0jRgMfBm4Bzbd6zhPfOAeQDDw8M1y5kyI/Ov73qby0+f29X2atTY77r9mdeHf5N1rbHqIKXtV2y/DZgN7CVplzW8Z4HtUdujQ0NDNcuJiAmakqsYtn8M3AwcMBX9RUR31LyKMSRp62Z5JvBe4OFa/UVE99Ucg3gjcGEzDrER8HXb11XsLyK6rOZVjPuA3Wu1HxH15U7KiChKQEREUQIiIooSEBFRlICIiKIEREQUtQoISbvWLiQi+k/bI4h/aeZ2+EtJW1WtKCL6RquAsL0P8GFgW2CxpK9Jem/VyiKi51qPQdh+FDgVOAl4F/AFSQ9L+uNaxUVEb7Udg9hN0ueAZcB+wAdsv6VZ/lzF+iKih9o+i3E2cB5wiu0XV2+0/bSkU6tUFhE91zYg5gIv2n4FQNJGwCa2f2r7q9Wqi4ieajsGcRMwc8z6ps22iBhgbQNiE9urJ6ClWd60TkkR0S/aBsRKSXusXpH0duDFcd4fEQOg7RjE8cAVkp4GBPwGcGi1qiKiL7QKCNt3SdoJ2LHZ9Ijtn9UrKyL6wUSmnNsTGGn22UMSti+qUlVE9IVWASHpq8BvAfcArzSbDSQgIgZY2yOIUWDn5uv0ImID0fYqxgN0BiYjYgPS9ghiFvCQpDvpfGs3ALYPrFJVRPSFtgFxWs0iIqI/tb3MeYukNwE72L5J0qbAtLqlRUSvtX3c+xjgSuBLzaZtgGtqFRUR/aHtIOXHgDnAc/CLyWNeX6uoiOgPbQPiJdurVq9Imk7nPoiIGGBtA+IWSacAM5u5KK8A/q1eWRHRD9oGxHxgBXA/8FHgBjrzU0bEAGt7FePnwLnNTyuStqVzK/Yb6JyOLLD9+ckUGRG90fZZjMdYw5iD7e3H2e1l4BO2l0jags50+TfafmhypUbEVJvIsxirbQL8CfBr4+1g+xngmWb5eUnL6FweTUBErCfanmL86DWbzpK0GPhkm/0ljQC7A3es4bV5wDyA4eHhNs0xMv/6Vu9ra/npc7vaXsSgaHuKsceY1Y3oHFG03Xdz4CrgeNvPvfZ12wuABQCjo6O5dBrRR9qeYvzzmOWXgeXAn65tJ0kz6ITDJbYXTri6iOiptqcY+060YUkCzgeW2f7sRPePiN5re5pwwnivFwJgDnAkcL+ke5ptp9i+YWIlRkSvTOQqxp7Atc36B4A7gUdLO9i+lc4M2BGxnmobELOBPWw/DyDpNOB620fUKiwieq/trdZvAFaNWV/VbIuIAdb2COIi4E5JVzfrHwIurFNSRPSLtlcxPiXpG8A+zaajbS+tV1ZE9IO2pxjQ+bLe55oHrp6UtF2lmiKiT7Sdcu5vgZOAk5tNM4CLaxUVEf2h7RHEQcCBwEoA208DW9QqKiL6Q9uAWNV8q5YBJG1Wr6SI6BdtA+Lrkr4EbN3McH0TE5g8JiLWT2u9itE8U3E5sBOdWa13BD5p+8bKtUVEj601IGxb0g22dwUSChEbkLanGEsk7Vm1kojoO23vpPxd4AhJy+lcyRCdg4vdahUWEb03bkBIGrb9BPCHU1RPRPSRtR1BXEPnKc7HJV1l++CpKCoi+sPaxiDGzucw3hT3ETGA1hYQLixHxAZgbacYb5X0HJ0jiZnNMrw6SLll1eoioqfGDQjb06aqkIjoPxN53DsiNjAJiIgoSkBERFECIiKKEhARUZSAiIiiBEREFCUgIqIoARERRQmIiCiqFhCSvizpWUkP1OojIuqqeQRxAXBAxfYjorJqAWH728D/1mo/IurLGEREFLWdtLYaSfOAeQDDw8M9riaivZH51/e6hOp6fgRhe4HtUdujQ0NDvS4nIsboeUBERP+qeZnzUuB2YEdJT0r6SK2+IqKOamMQtg+v1XZETI2cYkREUQIiIooSEBFRlICIiKIEREQUJSAioigBERFFCYiIKEpARERRAiIiihIQEVGUgIiIogRERBQlICKiKAEREUUJiIgoSkBERFECIiKKEhARUZSAiIiiBEREFCUgIqIoARERRQmIiChKQEREUQIiIooSEBFRlICIiKIEREQUJSAioqhqQEg6QNIjkr4raX7NviKi+6oFhKRpwDnA+4CdgcMl7Vyrv4jovppHEHsB37X9PdurgMuAD1bsLyK6rGZAbAN8f8z6k822iFhPTO91AZLmAfOa1RckPTLlNZzR6m2zgB/WraSsZY3d0NPPudoUfN6++Jy16YzWn/NNa9pYMyCeArYdsz672fZLbC8AFlSsoysk3W17tNd11JbPOVjW9XPWPMW4C9hB0naSNgYOA66t2F9EdFm1IwjbL0s6FvgmMA34su0Ha/UXEd1XdQzC9g3ADTX7mEJ9fxrUJfmcg2WdPqdsd6uQiBgwudU6IooSEOOQtK2kmyU9JOlBScf1uqaaJE2TtFTSdb2upRZJW0u6UtLDkpZJ+r1e11SDpI83v7MPSLpU0iaTaScBMb6XgU/Y3hnYG/jYgN8ufhywrNdFVPZ54N9t7wS8lQH8vJK2Af4aGLW9C52LBIdNpq0ExDhsP2N7SbP8PJ1fpoG8G1TSbGAucF6va6lF0lbAO4HzAWyvsv3j3lZVzXRgpqTpwKbA05NpJAHRkqQRYHfgjt5WUs1ZwInAz3tdSEXbASuArzSnUudJ2qzXRXWb7aeAM4EngGeAn9j+j8m0lYBoQdLmwFXA8baf63U93Sbp/cCzthf3upbKpgN7AF+0vTuwEhi4aQgkvY7Og5HbAb8JbCbpiMm0lYBYC0kz6ITDJbYX9rqeSuYAB0paTuep2/0kXdzbkqp4EnjS9uqjwCvpBMag2R94zPYK2z8DFgLvmExDCYhxSBKd89Vltj/b63pqsX2y7dm2R+gMZn3L9qT+4vQz2z8Avi9px2bTe4CHelhSLU8Ae0vatPkdfg+THIzt+dOcfW4OcCRwv6R7mm2nNHeIxvrpr4BLmueDvgcc3eN6us72HZKuBJbQuRK3lEneUZk7KSOiKKcYEVGUgIiIogRERBQlICKiKAEREUUJiPWQpF+XdE/z8wNJT41Z33iCbS2StE5zM0o6StKKpv+HJB3ThbaWSnpU0jclTeomn1h3uQ9iPWT7R8DbACSdBrxg+8zVr0uabvvlKS7rctvHSno98KCka23/z9p2KtR6ue1jm9f3BRZK2tf2wD152e9yBDEgJF0g6V8l3QF8RtJekm5v/hL/5+q7ByXNlHRZMxfC1cDMMW38QbPPEklXNM+gIOn05sjgPklnrrmCDtvPAv8NvEnS2yXdImlxcyTwxqa9RZLOknQ3nUfMx2vvZjo3+cxr9j1G0l2S7pV0VXO34BaSHmtui0fSlmPXY/JyBDFYZgPvsP2KpC2BfZrJg/cH/gk4GPgL4Ke23yJpNzp32yFpFnAqsL/tlZJOAk6QdA5wELCTbUvaerwCJG0PbA88DlwNfND2CkmHAp8C/rx568YTmI59CfDRZnmh7XObvv4R+IjtsyUtovO4+jV0bhdf2DyHEOsgATFYrrD9SrO8FXChpB0AA6v/mr4T+AKA7fsk3dds35vOd6je1rl9n42B24GfAP8HnN/MNFWabepQSb8PvETnP/MQsAtwY9PeNDqPHq92+QQ+l8Ys79IEw9bA5nRmTYfOPBYn0gmIo4FJj4PEqxIQg2XlmOV/AG62fVAzl8Witewr4Ebbh//KC9JedB74OQQ4FthvDfv/Ytyg2WdX4EHbpSndVha2r8nuvPqw0QXAh2zfK+ko4N0Atm+TNCLp3cA02w9MoP0oyBjE4NqKV7/J7Kgx278N/BmApF2A3Zrt3wHmSHpz89pmkn67GYfYqnlA7eN0pmlr4xFgSM2cj5JmSPqdiX4ISe+iM/5wbrNpC+CZZnzhw695+0XA14CvTLSfWLMExOD6DPBpSUv55SPFLwKbS1oG/D2wGMD2CjpBcmlz2nE7sBOd/5DXNdtuBU5o03nzje6HAGdIuhe4h/ZzEhzaXDL9L+AU4OAxVzD+hs6sXrcBD79mv0uA1wGXtuwn1iJPc8bAkHQInUHRI3tdy6DIGEQMBElnA+8D/qjXtQySHEFERFHGICKiKAEREUUJiIgoSkBERFECIiKKEhARUfT/THCpwxOpy0cAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=figsize)\n", "plt.hist(df_open['Trades Per Day'])\n", "plt.ylabel('Frequency')\n", "plt.xlabel('Trades Per Day')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Time spent in trade by market\n", "\n", "I calculate the percent of time spent in trades by summing up all trade durations. I then divide that number by number of minutes in the trading period. As can be seen almost half the trading period the algorithms had positions in 'GBPUSD' and 'USDCHF'. The least time was spent trading 'OILWTI'."]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["start = df['Open Date'].min()\n", "stop = df['Close Date'].max()\n", "\n", "total = (stop-start).total_seconds() / 60\n", "\n", "df_time = df[['Symbol', 'Duration']].groupby('Symbol', as_index=False).sum() \n", "df_time['Percent'] = np.round(df_time['Duration'] / total * 100)\n", "\n", "plt.figure(figsize=figsize)\n", "plt.bar(df_time['Symbol'], df_time['Percent'])\n", "plt.xticks(df_time['Symbol'], rotation=90)\n", "plt.ylabel('Time In Trade (%)')\n", "plt.xlabel('Symbol')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Orders\n", "\n", "There are 5 ways a order can get closed.\n", "1. By me. (Did not occur)\n", "2. By the algorithm.\n", "3. Market moves above/below take profit.\n", "4. Market moves above/below stop loss.\n", "5. <PERSON><PERSON><PERSON> closes trades open longer than one year. (Did not occur)\n", "\n", "So I have 3 situations to examine.\n", "\n", "#### Percent of stop loss  hits"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/plain": ["84.78"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(df['Stop Loss Hit'].sum() / df.shape[0] * 100, 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Percent of take profit hits"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"text/plain": ["11.96"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(df['Take Profit Hit'].sum() / df.shape[0] * 100, 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Percent closed by algorithms\n", "Specifically, this is the percent of trades closed because the trade triggered different logic than moving take profit and stop loss orders."]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"data": {"text/plain": ["3.26"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(np.sum((df['Take Profit Hit'] == 0) & (df['Stop Loss Hit'] == 0)) / df.shape[0] * 100, 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Stop order distance\n", "\n", "This histogram is very interesting as it shows a way I could potentially improve the logic of my algorithm.\n", "Placing orders 10% away from the market for the type of strategy the algorithms trade seems pointless."]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=figsize)\n", "plt.hist(df['Stops Distance'])\n", "plt.ylabel('Frequency')\n", "plt.xlabel('Distance (%)')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Drawdown\n", "\n", "In trading drawdown refers to the difference between the high point in a equity curve and succeding low point. I am interested in the the difference between the high point and the low point as well as the duration of the drawdown."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Max drawdown\n", "\n", "I define a helper function to locate the points in question."]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["def max_drawdown(arr):\n", "    \n", "    size = arr.shape[0]\n", "    arr = np.cumsum(arr)\n", "    start = np.argmax(arr)\n", "    stop = np.argmin(arr[start:])\n", "    \n", "    return start, arr[start], start + stop, arr[start + stop]"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["d1, v1, d2, v2 = max_drawdown(df['Profit'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Cumulative profit\n", "\n", "I draw a plot of the cumulative profit over time and mark the high and low points."]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["width = 800\n", "height = 400\n", "dpi = 100\n", "\n", "plt.figure(figsize=(width/dpi, height/dpi))\n", "plt.plot(np.cumsum(df['Profit']))\n", "plt.ylabel(\"Profit (zł)\")\n", "plt.xlabel(\"Number of trade\")\n", "plt.scatter(d1, v1, c=\"green\", label=\"Start of max drawdown\")\n", "plt.scatter(d2, v2, c=\"red\", label=\"End of max drawdown\")\n", "plt.legend(loc='best')\n", "plt.title('Maximum drawdown', fontsize=18)\n", "plt.tight_layout()\n", "plt.savefig('./img/drawdown.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Max drawdown duration\n", "I find that the duration of the max drawdown was:"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"data": {"text/plain": ["'4 days 03:48:00'"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["drawdown_dur = df.loc[d2, \"Open Datetime\"] - df.loc[d1, \"Open Datetime\"]\n", "str(drawdown_dur)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Time in max drawdown\n", "Percent of time spent in drawdown:"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/plain": ["14.85"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(drawdown_dur.total_seconds() / total / 60 * 100, 2) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Max drawdown amount\n", "The difference between the high point in the equity curve and the low point is:"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"text/plain": ["243.01"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["v1 - v2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Monte Carlo\n", "\n", "### Simulating trades\n", "\n", "<b>Assumption</b>:<br>\n", "* Future trades will be similar to the ones in the dataset. <br>\n", "* The algorithms trade size is fixed at 0.01 lots.\n", "\n", "The 'Profit Per Lot' column is sampled with replacement and summed up to simulate possible outcomes for the next 100 trades.\n", "\n", "I shall answer the following questions:\n", "1. What is the probability that over the next 100 trades the account will grow ?\n", "2. How much can I expect to lose in the worst case ?\n", "3. How much can I expect to gain in the best case ?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Below is a visual to explain the process. Each line represents a different 'future'. I am interested in the distribution of these 'futures'."]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=figsize)\n", "np.random.seed(12346)\n", "\n", "for _ in range(10):\n", "    \n", "    X = np.random.choice(df['Profit Per Lot'], replace=True, size=10)\n", "    X = np.hstack([np.array([0]), X])\n", "    plt.plot(np.cumsum(X))\n", "    plt.xlabel(\"Trades\")\n", "    plt.ylabel(\"Total Profit (zł)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["100000 samples are chosen with replacement and summed up to get the final profit."]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["nrows = 10**5 # Number of simulations\n", "ncols = 10**2 # Number of trades\n", "\n", "X = np.random.choice(df['Profit Per Lot'], replace=True, size=(nrows, ncols))\n", "X = np.cumsum(X, axis=1)\n", "X = X[:, ncols-1]\n", "density, bins = np.histogram(X, density=True, bins=200)\n", "X_unity = density / density.sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PDF from simulation\n", "\n", "I use the data from the simulation to create a pdf."]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjgAAAEYCAYAAABRMYxdAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAgAElEQVR4nOzdd3wU5fb48c9JBxJ6AKmhhKpIiYBYQIoCKkUFFVAUFH8qeq1XvF4V9HKvqKhfBStItaDYUBFFAVFEWmiCKIhIUaR3Ekhyfn/MJCzLppFsJuW8X699ZfeZZ2bPzu5Ozj5lRlQVY4wxxpjiJMTrAIwxxhhj8pslOMYYY4wpdizBMcYYY0yxYwmOMcYYY4odS3CMMcYYU+xYgmOMMcaYYscSHBNUIjJfRDYHYbtxIqIiMiKH9Ue49eN8ym5yyzr6lHV0y27K55DzjYjcISLrRSTZ/zUFqBuU/W8yVxQ+Q+bMBTpumMLJEpwSxOfA63s7LCLLReQfIhLqdYyFkZtMjRCRFoUglkuAccB64P8BNwC7PA2qBCpMn4nC4Ez2h4hEi8jjIjJTRLa5x6P52azTQ0R+EJEjIrJXRN4XkbqZ1G0kIh+LyD63/nci0imXL80UYWFeB2A88Q4wCxCgOnAT8ALQDBjqXVhB9R/gKSA5m3oLgFLACZ+yOOBxYDOwMgix5UZX9+9gVd3raSQlWxyF5zNRGMSR+/1RGRgB/A0sB6pmVVlErgJmAKuAB4FywD3AQhFJUNU/ferWB34AUoCngQPArcCXItJdVb/OYYymCLMEp2RKVNVp6Q9E5BXgZ+AWEXlUVf8OtJKIxKjqoYIKMj+pagrOwS67emlAUvAjOmPVACy5CS4RCQdCVbUwfxaKur+AWqq6DUBEDmdW0X0/XgK2Ahep6mG3/Auc5GgEp/44+x9QHmitqivdulOAtcA4EWmsdhr/Ys+6qAyqehBYhNOiUw9ARDa74zdaisiXInIAWJ2+johcLCJzROSAiBwTkUQRGZLZc4hIPRH5xK1/UEQ+EpF6fnVCROQREVkgIjtE5LiIbBGRV0SkUhbbvl5EVotIklt/hIiE+dU5bQxOJts6ZfyE+3eeu3iiT9de+r5RERmVybY+d19rmaye063bW0QWuk3ph937vXyWx4mIAje7jzPiyG7bmTxfjt4/EWnmdgNsd8f87BCReSJyuU+dKHf//iIiR0Vkv4isEZFnchhLGRH5n4j85vMcU0Skjk+dJu7rfS6Tbbzjfl5ifcrOcj87W9xlf4rI6yJSxW/d9M9GMxF5TkS24SS57TJ5rpvI5DMRoO7NIrLWfV1/iMg/M9lmgvud2O3W/cX9LuToR6j7/JNE5HwR+db9HO0RkfEiEh2gfrb7RkQuF5E0EZngt260G9/fIlItN/vDl6ompyc3OdABp7V5fHpy425jJTAfuFacJAj3+9YTmJ+e3Lh1DwPjgYbAeTl5UhG5VU6Od9soIvfgHCf961UXkTEislKcLrEkEVknIg+JT9e/iPRx982tmTzfWvd5xH2c7ffPZM5acAzul6mB+3C3z6LawFzgfeADINqtfyXwEbADGAMcAq4DxotIPVV9xO8pyuAchBYDDwPxwB1AOxFpqao73HoROE3PHwCfAEdwDkRDgAtFpLWqHvfbdk+cpGycG09PnKbyOrjJQB4tAP4L/At4HfjOLf9bVVeIyHJgkIg8pqqp6SuJSA3gMuBNVT2S1ROIyB2cHFfzhFt8E/CxiNymqq/jjLO5AedX6kXufXCa93Mlp++fOEnlXHe1V4E/cLoVEoC2wOfusnHAYGAK8BzOcSUeyHa8g/tP6UvgApzuhzHuurcDl4rT9bBNVX8WkaVAfxF50G9flwV6AV+o6i63rDZO0h4BTAB+w/mM3w5c4m73gF84bwHH3BgUp4UhkEw/E371/h9Ot8sEYD8wEBgtIttU9W2f+C8HPgQ2us+9Fzgf57PQAuib+R48RQvgM2Ai8DbQEee7k4ZP60ZO942qfi4iLwD3isgcVX3X3cTLOO9RD1XdISI53R95kZ6QLAqw7Eecz1pDnBaa5kBkFnXTt7ckqyd0k5nncbrE/gWUBh4Adgao3hy4Cud79RsQDnTD6RavB9zm1vsU53s3GHjD7/naAU2BR1RVc/H9M5lRVbuVkBvOAU+Bx3C+KLE4X8w33PJFPnU3u2W3+G0jFOeLth+o7lMeASwEUoF4n/L57nZe8NtOH7f8VZ8yAUoFiHuIW7efT1mcW5YKtPLbxkfusnY+5SPcsjifspvcso4B9tFNWZX5LBvqLuvhV/6IW94mm/ekAnAY559bWZ/ysjgHykNAeZ/ySc7XNsfv+Xxg85m8fzjJ4in7PZPn2AvMOsPP5K3uczztV365Wz7Vp+zOTPZ1+ufjKp+yT3D+EdX0q5uA01U5IsBnYz4QlsvvUqDPRPqyP4FyPuWlcRJV3+9ZFM4/vAX+zw3c6//5zCIexUlk2vqVf44zniz6DPdNBLAMZwxLPZzEWoFnc7o/cvFZOIzT6hJo2Uvu9psEWHaHu+xS9/HV7uPbA9Rt6i77bzaxlMf5gbUOKO1TXtON0/+4UQqQANuZ6n6nzvIp+6+7flO/um+4+796br5/dsv8Zl1UJdNInAPtTpxfJ4OBmUBvv3p7cX4N+mqN07LzpvoM6lOnZeVpnG7PXpzuKd8HqvoR8Ivvc6rjGICIhIpIeRGpzMlfMW0DbHeOqib6bsONA5wkKtjexjngZXTvuC1ig4E1qprlr0ScQcNlgBfV6SoEMroNX8RpNeuSj/Hm5v1Lb+Ho7raSZOYA0ExEzj6DePrg/GP+n2+hqn6OM1i1l4ikH6feAY4DN/pt40acz+pnACJSDrgC5zOdJCKV0284iftG4NIAsbygzlit/DJRfVqJVPUoTgtCvE+drjitPBOB8n6xznLrBIo1kEWqutivbC5Oi1oc5H7fuJ+Lazn5w+FlnITn4RzGlF9Ku38DTRJI8quTm7qZudStM8593wBQp0vtLf/KqnrMPfYgIhEiUtHdp1/ifKcSfKqn/6D0PWaUwdnPX/h8L3P6/TOZsASnZHod58DaBacpPFZVe+npg4t/U5+uAFf6lMy1AbabXlbPr3y/nuyG8vUzUFV8xqiISD8RWYzTVbAPJxHb5C6ukMk2/K3LJI58p06//jvAlXJy/EdH97knZLaejzPZn3mR4+dT1W9xup1uAnaLMy5opIg09VvvHpz3Zo0442jGi4hvYpJdPH+q6r5M4onBaW1EnYHVn+EkPWXBGZuE02X3rp7svmyEc2wbgvP58b81IvCMnV9zEG9ubApQtgfwHU/WxP37JqfHud5dluXsohw8Hz7Pmet9o6q/AffhtPaGAterqu8sw4KQnmREBlgW5VcnN3Uzk/6dWx9g2Tr/AhEJE5F/i8ivOEnUHpz9OdWtknHsUtXfga+BG9LHDQH9cD7r433q5fT7ZzJhY3BKpg2as2mS2R0E8pU400Cn4/SN/wNnxkQSzkF1NoU3IX8dp6vlRpwxFENwfj1OzWqlokBVB4kzWLg7TiJxP/CIiNyjqmPdOp+4iUYPnMGgXXD2wXci0kVPHzeVF1Nwxjr0w/lncANO68Jknzrpg0Cn+ZX7OhagLL8/7/4/DgJJj/VBMp9e/Wcm5bl5PvH7m9t9c6X7txROErQxhzHll/R9UIPTf9TUcP9uD1DXn3/d/PIccBfO8WsUTuv4CaAVMJrTj12v44xt7Ikz5nAITlflKeNqcvL9M5mzBMfkVvqvxGYBljX1q5OuvIhUC9CK0wTYqScH4d6Ak9Bc4tssLCKNs4inSYCyzOI4U1lOJ1XVZSKyAhjizji5GvhYczaV23d/fuO3LL9fh//z+Qv4fKr6E/AT8IyIlMcZLP6UiIxLb5Z3X+s0YJrbRfcU8E+c7q73s4mnm4iUV9X9AeI5yKkD32e5j2/kZIKz3q8rcCPOexaRw0T+TOTXFOMN7t8jQYzVV673jYjchfOP+Cmc5HKSiDRXVd9B2MGecr3U/Xs+TuuHr3Y4n5P0Frg1OD8wzg+wnfSZccuyeb7070BjMv9e+roBWKCq1/kWikiDAHXh5DioISLyE84g+9GBukhz8v0zgRXWX8Sm8EoEtgA3i0i19EK3qfVBnAPdJwHWG+77QET64PwS/NinONVdP8SnngD/ziKeriLSyq9++lTcjwOvkmvp01IrZlHnDZxk6yWcZvDxWdT1NQdnMONdIhKTXujev8t97jm5DTgLOX7/3HEEpxwj3CTkd5zxCVHpY6X86iiwwn2Y1T4D5z0K4fTPR3egJTBTnXMTpW/7BM64pwtFpD/OeJZTWiJUdQ9OInSVOzPlFOKI9S/PpZx8JnLiS5x/dMNF5LRtiUgp389FXuV234jIucAzONPAH8GZbVcWmOr32civ/ZGZb3Fmtd0iPtPe3fg6Au+nd5u53cafAh3d5el1o4FbcJLK7MbGzcFpybpTRDLG64hITaB/gPqp+E0fd7ve7w20cTfWSTgzLR93i/2n42f7/XPrhYtIY3d2nPFhLTgmV1Q1VUSG4Qw4XCoir+PM9LkW59fRf1V1g99qu3EOqNVxZqqkTxP/G2cGS7oZOK0fc8U5KVc4ziDkrAYErnLrj8M5APbC6SKZqqqBpomeiXU4r/EOETmKMwNpp6rO9anzFs4/goE4ByD/X30Bqep+cc6NMg5YLCKT3EU34UzdvU1Pn858xnL5/t2IM0X4I5xf/idwuqAuA95T1WNucvOXiMzESWp24oyruR1nDNWn2YQ0CRgEPOR2cy3Aed3pn49/BVhnMnA38ArOAOVpAercDnwPLHA/SytwEql6OJ+RKZz62cutnHwmsqWqR0TkRpxE7xcReRNnX5fHaT24Cmcg9vw8xOovR/vG/Qf9Lk7ryEA30VwhIg/hTJ9+iJODw89of7ifxfQEORyoIyLpP2hWqeqn4CQEIvIPnC6g70TkDZxE616csS6Pn7plHgY6A1+JyPPua7gVp4vq8uxaPlR1n4g8CjwL/ODup9I4U/834CTfvmYAt4nIdJwWpqo4Ew32kLk3cH5UXA98G+C4me33z62X3m33LU6yZ9IV9LQtu3l34+RUzgdyUHczmUzZdJd3wPmVcxCnW2kFMCRAvfnuturhtAwcxDkQfgI0CFD/VpyDZRJOwvI6zq9CBSb51Itzy0bgHCBW4zRLb8U5f0i433ZHcIbTxN3yHjitH0nu8tP2Dc4vMAUePYP3pg/OqeWPuLcfgN4B6k0iD9PEc/P+4ZxXZTLOwfWIW3cVzjiASLdOBM4/uSU4B/Nk9/1+E5/TBWQTYxl3G5twZkntxBm/VCeLdda4+3pOFnUq4ySd6QM/97vr/R8+U3QDfTZyGHfAz0Rmn6Gs3j/gbJxEbbu7D/52PwOPAhVzEMsp34+sPuM53Tfue5jG6dPyBWew9wl8pqXn5DsSIL7Nbt1At0Cv5wqcmWhHcRLoGUD9TLbdBOc4s9+t/z3QJZfv8W04sz2T3e/BPTjn1/I/bpR29+cf7uvfgNMq2Tmzz4K73jfu8hsCLMv2++fWi8vp/i5pN3F3kDEmj0TkZZzz4sRpzs/QaowpoURkFs5Yoep6skXG5BMbg2NMPhDn/CIDcc5jYcmNMSZL7gDky4BpltwEh43BMSYPxDm5XUuccSTROGcpNcaYgESkLU732d043ZFjvI2o+LIWHGPy5hqcQZmNgTs0/wY2G2OKp9txxjeVBQao6mZvwym+bAyOMcYYY4qdEt1FVblyZY2Li/M6DGOMMcacgeXLl+9W1YDntSrRCU5cXBzLlmV3QktjjDHGFEYi8kdmy2wMjjHGGGOKHUtwjDHGGFPsWIJjjDHGmGLHEhxjjDHGFDuW4BhjjDGm2LEExxhjjDHFjiU4xhhjjCl2LMExpoRTVVJTU70Owxhj8pUlOMaUACtWrODw4cMBl/33v/+lSZMmpKWlFXBUxhgTPJbgGFPMLViwgFatWtGmTRvWr19/2vKvvvqKDRs2sHbtWg+iM8aY4LAEx5hi7MSJE9x5553UqFGD3bt306ZNG2bOnJmxPC0tjcTERAC++eYbr8I0xph8ZwmOMcXY2LFj+emnnxg3bhzLly+nQYMG9O/fn+TkZAB+/fXXjK6ruXPnehmqMcbkqxJ9sU1jipu0tDRGjRpFeHg4DRo04PHHH6d79+707NkTEeHxxx+nd+/eLF68mIsvvjjjYrPt27fn22+/JSUlhbAwOywYY4o+O5IZU4y8//77PPbYYxmPIyIiePHFFxERADp06EBISAjffPMNF198McuXL6dUqVLceeedDBgwgOXLl9O2bVuvwjfGmHxjXVTGFBOpqak8/vgI6jRoxJ49e/j+++/58ccfadCgQUad8uXL06pVq4zuqGXLltGyZUu6du0KWDeVMab4sATHmGLi3Xff5Zdf1nPdbfdRsWJFLrjgAlq2bHlavc6dO/Pjjz9y8OBBEhMTSUhIIDY2lubNm9tAY2NMsWEJjjGF2EsvvUTr1q3Zs2dPwOVHjx4lNTWVlJQURowYSY16jTm/c48st9mpUydSUlIYP348R48epXXr1hnlCxcuJCkpKaOuqvLqq6+ybdu2/HtRxhhTACzBMaYQ++yzz0hMTOSqq6/m+PHjGeX79+/n/vvvp3z58kTHxNC0WTM2btxA14HDCAnJ+mt9wQUXEB4ezvPPPw9AQkIC4CQ4SUlJLFq0KKPuhx9+yO23385//vOfILw6Y4wJHlFVr2PwTEJCgqbPIjGmMDrrrOqckDD2/LWVm2++mQEDBjBnzhzGT5jA3j17SOjSm9Ix5fhr8waiy1VgwPBnia8aw5XnVs9yux06dGDBggWUKVOGAwcOEBoaysGDB6levTpt27Zlzpw5pKam0qxZMzZs2ED58uX566+/iIqKKqBXbowx2ROR5aqaEGhZUGdRiUg34P+AUGC8qj7ltzwSmAK0BvYA16rqZhGpBMwAzgMmqeowt34M8J3PJmoC01T1HhG5CXgG2O4uG6uq44P24owJst27d7Njx19cees/ST52hIkTxzFx4kRCQ8OIb3k+g564j5oNmp7Rtjt16pRxhuPQ0FAAypYty4svvsiQIUMYM2YM0dHRbNiwgbvvvpsXX3yRTz/9lL59++bnSzTGmKAJWoIjIqHAOKArsA1YKiIzVXWdT7UhwD5VbSAi1wGjgWuBJOBR4Gz3BoCqHgJa+DzHcuBDn+1NT0+GjCnq1qxZA0D1uo1o2PoCKlatQXSFytQ/J4HIUmXytO3OnTszYsSIjPE36W6++WZmzZrFI488QkxMDBdddBHPPfccH3zwAVOmTMlIcNKTo+jo6DzFYYwxwRLMMThtgI2quklVjwPvAr386vQCJrv3ZwCdRURU9Yiqfo+T6AQkIg2BKpzaomNMsZGe4JxVtyEiQpvLrqZpmw55Tm4A2rZty4ABAxgwYMAp5SLC66+/TpUqVdi7dy/PPPMMoaGhDBw4kC+++IKdO3cyevRoOnToQL9+/ewCncaYQiuYCU4NYKvP421uWcA6qpoCHAAq5XD71+G02PgOIrpaRFaLyAwRqRVoJREZKiLLRGTZrl27cvhUxhS8NWvWEF2uAjEVY/N92+Hh4UybNi1jgLGvihUrMnv2bN56662Mk/7dcMMNpKam0rdvX4YPH87ZZ5/NF198kTFQ2RhjCpuiPIvqOuAdn8efAnGq2hyYw8mWoVOo6uuqmqCqCbGx+f+Pw5j8snr1Gs6q2yjjLMQF6eyzz6Z///4Zj5s1a0br1q1ZsGABPXv2dGZ2XXUVw4cPZ+nSpQUenzHGZCeYCc52wLcVpSYnBwCfVkdEwoByOIONsyQi5wJhqro8vUxV96hqsvtwPM7AZWOKpLS0NH5a+xPV4uK9DiXDU089xV133cX06dMJDw9n/Pjx1KhRg4EDB1pXlTGm0AlmgrMUiBeRuiISgdPiMtOvzkxgkHv/GmCu5mze+vWc2nqDiJzl87An8PMZRW1MIfD7779z9MgRzqrbyOtQMnTp0oUXX3wxY6p4hQoVePLJJ/n1119ZsmSJx9EZY8ypgjaLSlVTRGQY8CXONPE3VXWtiDwBLFPVmcAEYKqIbAT24iRBAIjIZqAsECEivYFLfWZg9QP8T9d6t4j0BFLcbd0UrNdmTLBlDDCOa+hxJFm78sorCQ8P54MPPqBdu3Zeh2OMMRmCOgZHVWepakNVra+qo9yyx9zkBlVNUtW+qtpAVduo6iafdeNUtaKqRqtqTd/p5apaT1XX+z3Xw6raTFXPVdVL/JcbU9hNmDCByy+/giNHjrBmzRpEhGpxDbJf0UPly5ena9euzJgxg5J80lBjTOET1BP9GWNy5tixYzw0fDh7du/mpptuAqBy9Vr5MiU82K6++mpmzZrFihUraNWqldfhGGMMULRnURlTbEybNo09u3fTokMPZsyYwYcffkjVOoW7eypdr169CA0N5YMPPvA6FGOMyWAJjjEeS0tLY8yY56gV34wb/vUcrTv3JC0tjbPqFo0Ep1KlSlxyySXWTWWMKVQswTHGY7Nnz+aXX9Zz8dU3IyL0u/c/dLxmMK079/Q6tBy7+uqr+fXXX1m7dq3XoRhjDGAJjjGee3bMGCrEVqPFxd0ACI+IpOfQh6hSs67HkeVcnz59CAkJYdq0aV6HYowxgCU4xnhqy5YtzJs7l/Ov6E9oWLjX4ZyxqlWr0rt3b8aPH8+xY8e8DscYYyzBMcZLixYtAqBR6ws8jiTvhg0bxp49e5g+fXpG2eLFizly5IiHURljSipLcIzx0KJFi4iIjKJ6vcJzxuIz1bFjR5o2bcpLL72EqjJlyhTatWvH2LFjvQ7NGFMCWYJjjId++GERtRqeXaS7p9KJCMOGDSMxMZExY8Zwyy23AE4rjjHGFDRLcIzxSFJSEitXrqB2kxZeh5JvbrjhBsqWLcuDDz5I3bp1ueyyy1i+fHn2KxpjTD6zBMcYjyQmJnLixAnimrT0OpR8Ex0dzV133UWVKlX47LPP6Ny5M1u2bGH37t1eh2aMKWEswTHGI+kDjOs0OdfjSPLXk08+yZYtW4iPj6d169aAk8wZY0xBsgTHGI/8+OOPVKpWk7IVY70OJV+JCJGRkQAZ16aybipjTEGzBMeYApKamsqrr77KH3/8ATgDjIvT+JtAypcvT7169SzBMcYUOEtwjCkgzz//PLfffjsXXngRCxYs4M8/txNXzBMcgNatW1uCY4wpcJbgGFMA1qxZwyOPPEL95m3Yd/AQXbt2BYrf+JtAWrduzebNm9mzZ4/XoRhjShBLcIwJsuTkZAYOvIGoMjEM+vf/cdv/3iQ8spR7gr/GXocXdOkDjVesWOFxJMaYkiTM6wCMKe7GjBnD6tWrGDLyFaLLVyS6fEXu+r/pHNy7i7DwCK/DCzrfgcZdunTxOBpjTEkR1BYcEekmIr+IyEYRGR5geaSITHeXLxaROLe8kojME5HDIjLWb5357jZXurcqWW3LGK9Nm/YWDc5tS7PzO2WUValZlwbN23gYVcGpWLEidevWtXE4xpgCFbQER0RCgXFAd6ApcL2INPWrNgTYp6oNgOeB0W55EvAo8EAmmx+gqi3c285stmWMZzZu3MjPP6/j7PadvQ7FU61atWLhwoXs2rXL61CMMSVEMFtw2gAbVXWTqh4H3gV6+dXpBUx2788AOouIqOoRVf0eJ9HJqYDbOvPwjcm7Tz75BICzzy/ZCc7gwYPZvXs355xzDrNmzfI6HGNMCRDMBKcGsNXn8Ta3LGAdVU0BDgCVcrDtiW731KM+SUyOtiUiQ0VkmYgss1+TJtg+/uQTatRrTMVqNb0OxVM9evRg6dKlVKlShcsvv5x33nnH65CMMcVcUZxFNUBVzwEucm835GZlVX1dVRNUNSE2tnidQdYULrt37+aHhQtp6jP2piRr3rw5S5Ys4ZxzzuGZZ55BVQHnBIj//ve/+fnnnz2O0BhTnAQzwdkO1PJ5XNMtC1hHRMKAckCWJ8tQ1e3u30PA2zhdYWe0LWOC6bPPPiMtLa3Ed0/5ioqK4o477mDFihUsXboUgHfffZdRo0bx2muveRydMaY4CWaCsxSIF5G6IhIBXAfM9KszExjk3r8GmKvpP+sCEJEwEans3g8HrgB+OpNtGRNsM2fOpEJsNWrGN/M6lEJlwIABREdH88orr3DixAkef/xxwLk2lzHG5JegnQdHVVNEZBjwJRAKvKmqa0XkCWCZqs4EJgBTRWQjsBcnCQJARDYDZYEIEekNXAr8AXzpJjehwNfAG+4qmW7LmIJ27NgxZn/5Ja0698bGup8qJiaGAQMGMHnyZJo0acJvv/1GixYtWLFiBcnJyRkX6jTGmLyQktzIkZCQoMuWLfM6DFMMvfPOO/Tv35/bn55MfIt2BfrcDapEc+W51Qv0OXNr5cqVtGzZEoB27drx4IMPcvXVV7No0SLatSvY/WWMKbpEZLmqJgRaVhQHGRtT6E2cOIlK1WpQv4SczC+3WrRokZHIjBo1KuO+dVMZY/KLXarBmHy2bds2vv56Dl0H3EFIiP2GyMyYMWOYP38+nTo5s8xq165tCY4xJt9YgmNMPps6dSqqSkKX3l6HUqi1b9+e9u3bZzxu164dixYt8jAiY0xxYj8vjclHqsrEiZOo3/w8Klev7XU4RUq7du3YsmULf/75p9ehGGOKAUtwjMlHixcvZsOGXzmvax+vQyly0sfhLF682ONIjDHFgSU4xuSjSZMmERlViuYXXeZ1KEVOy5YtCQ8Pt3E4xph8YQmOMfnk2LFjvPPuu5xz4aVElY72OpwiJyoqilatWlmCY4zJF5bgGJNPPvnkEw4eOGDdU3nQvn17lixZwoEDB7wOxRhTxFmCY0w+mThxEhWrVqf+uW29DqXI6t+/P0lJSUybNs3rUIwxRZwlOMbkg+3bt/P113No3aW3nfsmDxISEmjVqhWvvfYaJfks68aYvLMjsTH5YNq0aaSlpdm5b/LB0KFDWbNmDUuWLPE6FGNMEWYJjjF5lH7um3pntya2Rh2vwyny+vfvT5kyZXjttde8DsUYU4RZgmNMHqgqw4YN45df1tOuRz+vwykWYmJi6Py3U/EAACAASURBVN+/P++++64NNjbGnDFLcIw5Q6rK3Xffzcsvv8wlfYfQunMvr0MqNm677TaOHTvGQw89RGpqqtfhGGOKIEtwjDlDL7zwAmPHjqXjNYO54pYHERGvQyo2WrduzX333cdrr71Gv379OHbsmNchGWOKGEtwjDlDM2fOpGaDplx56z8tuQmCMWPG8Pzzz/PRRx9x+eWX26wqY0yuWIJjzBlIS0tj2fLl1GnSwpKbILrnnnt45plnmDdvHqtWrfI6HGNMEWIJjjFnYMOGDRw+dIhaDc/2OpRib9CgQYSGhjJ9+nSvQzHGFCGW4BhzBpYtWwZATUtwgq5y5cp06dKF6dOnWzeVMSbHgprgiEg3EflFRDaKyPAAyyNFZLq7fLGIxLnllURknogcFpGxPvVLi8jnIrJeRNaKyFM+y24SkV0istK93RLM12ZKtqVLlxIRGUXV2vW9DqVE6NevH7///jvLly/3OhRjTBERtARHREKBcUB3oClwvYg09as2BNinqg2A54HRbnkS8CjwQIBNP6uqjYGWwAUi0t1n2XRVbeHexufjyzHmFEuXLaNGg6aEhoZ5HUqJ0KdPH8LDw62byhiTY8FswWkDbFTVTap6HHgX8D9RSC9gsnt/BtBZRERVj6jq9ziJTgZVPaqq89z7x4FEoGYQX4Mxp0lJSWHFihXWPVWAKlSoQNeuXXnvvfesm8oYkyPBTHBqAFt9Hm9zywLWUdUU4ABQKScbF5HywJXANz7FV4vIahGZISK1MllvqIgsE5Flu3btytkrMcbH+vXrOXb0KLXiLcEpSNdeey1btmxh8eLFXodijCkCiuQgYxEJA94BXlTVTW7xp0CcqjYH5nCyZegUqvq6qiaoakJsbGzBBGyKlfQBxjaDqmD16tWLiIgI3nvvPa9DMcYUAcFMcLYDvq0oNd2ygHXcpKUcsCcH234d2KCqL6QXqOoeVU12H44HWp9h3MZkadmyZUSVLkNszbpeh1KilCtXjm7duvHee++RlpbmdTjGmEIumAnOUiBeROqKSARwHTDTr85MYJB7/xpgrmbTwS4i/8FJhO7xKz/L52FP4Oc8xG5MppYuXUaNBs0ICSmSDaBFWr9+/di+fTs//PCD16EYYwq5oB2h3TE1w4AvcZKN91R1rYg8ISI93WoTgEoishG4D8iYSi4im4HngJtEZJuINBWRmsAjOLOyEv2mg9/tTh1fBdwN3BSs12ZKruPHj7Nq1UrrnvJIz549iYyMtG4qY0y2gjrHVVVnAbP8yh7zuZ8E9M1k3bhMNhvwvPiq+jDw8BkFakwOffDBByQnJ9Pg3LZeh1IixcTE0KNHD95//32ef/55QkNDvQ7JGFNIWRu7MTmkqjz99DNUrVWPxudd7HU4Jda1117Ljh07+P77770OxRhTiGXZgiMi92W1XFWfy99wjCm85s2bx8qVK+h7z5M2/sZDl19+OaVKlWL69Ol06NDB63CMMYVUdkfpGPeWANyOc96aGsD/A1oFNzRjCpenn36GshUrk9DF/3yVpiBFR0dzxRVX8PbbbzNv3jyvwzHGFFJZJjiqOlJVR+JM8W6lqver6v04U7BrF0SAxhQGa9as4csvZ3NBz4GER0R6HU6JN2rUKKpVq0aXLl0YOXIkycnJ2a9kjClRctrOXhU47vP4uFtmTIkwevRoIqNK0f6K670OxQDx8fEsW7aMAQMGMGLECMqWLcv555/Po48+yr59+7wOzxhTCOQ0wZkCLBGRESIyElgMTApaVMYUIj/99BNvv/02F/QaSJmy5b0Ox7iio6OZPHkys2fP5h//+AdhYWGMGjWK+Ph4Xn75ZVJTU70O0RjjoRxNE1fVUSLyBXARoMDNqroiqJEZU0g89thjRJWO5pK+t2Rf2RQoEeGyyy7jsssuA2DlypXce++93HnnnRw8eJDhw4dnswVjTHGVm6kgqUCaz82YYm/ZsmV89NFHXHzVTdZ6UwS0aNGCuXPn0qlTJ1577TW7pIMxJViOEhwR+QfwFlAZqAJME5G7ghmYMYXBv//9KNHlKtDhqpu8DsXkkIhwyy23sHnzZubOnet1OMYYj+S0BWcI0FZVH3fPRNwOuDV4YRnjvcTERL78cjYd+w4hqky01+GYXOjTpw8VKlRgwoQJXodijPFIThMcwemiSpdKJpdMMKa4GDt2LJGlSnN+j2u9DsXkUlRUFAMGDOCjjz5i7969XodjjPFAThOcicBin1lUP+JcKNOYYmnPnj28/fbbtO7ck1LRZb0Ox5yBIUOGkJyczFtvveV1KMYYD+QowXEvyXAzsBfYjTOL6oVgBmaMlyZMmEBycjIX9hzodSjmDLVo0YJWrVpZN5UxJVRuZ1Gpe7OpCabYSk1NZdy4l4lv0Y5qcfFeh2PyYPDgwaxatYrVq1d7HYoxpoDZLCpj/Hz22Wds2fIHF/Qc4HUoJo/69etHaGiodVMZUwLZLCpj/EybNo1ylarQ7PxOXodi8ig2NpZu3brxzjvv2DlxjClhbBaVMT5SU1P5+ptvaJRwIaGhOTrRtynkBgwYwNatW/nuu++8DsUYU4DOZBbVCGwWlSmmEhMT2b9vH/Et23sdisknPXv2pEyZMtZNZUwJk5tZVINxZlHtJYezqESkm4j8IiIbReS0i8KISKSITHeXLxaROLe8kojME5HDIjLWb53WIrLGXedFERG3vKKIzBGRDe7fCjl5bcb4mjNnDgANLcEpNsqUKUOfPn14//33SU5O9jocY0wByc0sqpXADOBjYI+I1M6qsoiEAuOA7kBT4HoRaepXbQiwT1UbAM8Do93yJOBR4IEAm34FZ/xPvHvr5pYPB75R1XjgG/exMbny9ddfU6N+Y2IqVPI6FJOPBg4cyP79+5k1a5bXoRhjCkhOZ1HdBfwNzAE+Az53/2alDbBRVTep6nHgXaCXX51ewGT3/gygs4iIqh5R1e9xEh3fOM4Cyqrqj6qqwBSgd4BtTfYpNyZHjh49ysKFC617qhjq3LkzVapUsW4qY0qQnLbg/ANopKrNVLW5qp6jqs2zWacGsNXn8Ta3LGAdVU0BDgBZ/XSu4W4n0Darqupf7v0dQNVAGxCRoSKyTESW7dq1K5uXYEqSBQsWcPz4cRq2sgSnuAkLC+O6667js88+48CBA16HY4wpADlNcLbiJB9Fgtu6o5kse11VE1Q1ITY2toAjM4XZ119/TVh4BPXOTvA6FBMEAwYMIDk5mQ8++MDrUIwxBSDLebAicp97dxMwX0Q+BzJG6bmDjzOzHajl87imWxaozjYRCQPKAXuy2WbNTLb5t4icpap/uV1ZO7PYjjGn+eqrOdRt1oqIqFJeh2KC4LzzziM+Pp5p06YxePBgr8MxxgRZdi04Me5tC874mwifsphs1l0KxItIXRGJAK4DZvrVmQkMcu9fA8x1W18CcrugDopIO3f21I3AJwG2Ncin3JhsLV++nDVrVtv4m2JMRBgwYADz589n+3b/31rGmOJGssgn8r5xkR7AC0Ao8KaqjhKRJ4BlqjpTRKKAqUBLnOnn16nqJnfdzUBZnKRqP3Cpqq4TkQRgElAK+AK4S1VVRCoB7wG1gT+Afqq6N6v4EhISdNmyZfn9sk0Rs2/fPlq1as2Bo0nc9/JHlClbtM8w0KBKNFeeW93rMAqljRs3Eh8fzzPPPMMDDwSapGmMKUpEZLmqBhxXkGWCIyIvqOo9IvIpAca0qGrP/Auz4FmCY1SVPn368Pnns7hjzDTimrTwOqQ8swQna23btiU5OZmVK1d6HYoxJo+ySnCyOxf9VPfvs/kbkjGFw8svv8wnn3xC79v/VSySG5O9G2+8kWHDhpGYmEirVq28DscYEyRZjsFR1eXu328D3QomRGOCIzU1ldGjn6Z+8/O4qPeNXodjCsiAAQMoVaoUb7zxhtehGGOCKMsEx70kwuoAtzUisrqggjQmGGbPns3WrVu4sOdA3Ct+mBKgfPny9O3bl7feeovDhw97HY4xJkiy66K6okCiMMYDr7zyCuUqxnJ2+85eh2IK2NChQ5kyZQrvvfeeTRk3ppjKrovqj/SbWxTv3t+JM+vJmCLpjz/+YNasWbTpdg2hYeFeh2MKWPv27WnSpAmvv/6616EYY4Ikp9eiuhXnWlGvuUU1cS66aUyR9MYbbyAitOvRz+tQjAdEhKFDh7J48WJWr7bedmOKo5xequFO4ALgIICqbgCqBCsoY4Jp27ZtvPHGeBq36UCFKjaduqS64YYbiIiIsMHGxhRTOU1wkt0rggPgXlYheGcINCYIVJU33niDps2aceDgQTpfe6vXIRkPVapUiWuuuYapU6dy9OhRr8MxxuSznCY434rIv4BSItIVeB/4NHhhGZP/HnjgAYYOHUq1ek154LVPqdustdchGY/deuutHDhwgBkzZngdijEmn+XoUg0iEgIMAS4FBPhSVYt8u66dybjkWL9+PWeffTatu/Sm373/ISQkp7l90WNnMs45VaVRo0ZUqVKF77//3utwjDG5lNWZjHN6lB+hqm+oal9VvQZ4U0Teyr8QjQmuBx/8JxFRpbliyAPFOrkxuSMi3HrrrSxcuJB169Z5HY4xJh/l9EhfS0QeBnCvDP4BsCFoURmTj+bNm8dnn31Kp+uGEl2+otfhmEJm0KBBhIeH22BjY4qZnCY4g4Fz3CTnM+BbVR0RtKiMySdpaWncf/8DVKxa3S7HYAKqUqUKvXv3ZsqUKSQlJXkdjjEmn2R3qYZWItIKaAn8H3AtTsvNt265MYXa+++/z4oViXQbdA8RkVFeh2MKqVtuuYW9e/fy8cd2ei9jiossBxmLyLws1lVV7ZT/IRUcG2RcvKWkpNC0aTMOp8D9r3xSYsbe2CDj3EtLS6Nu3bo0bNiQOXPmeB2OMSaHshpknOW1qFT1kuCEZEzwTZ06lQ0bfuXmx8eVmOTGnJmQkBBuvvlmRo4cyebNm4mLi/M6JGNMHmXXRTXQ/XtfoFvBhGhM7iUnJzNixEhqNzrbLqZpcuTmm29GRJg4cSIAS5YsYcKECR5HZYw5U9ldTbyM+zcmwDI7k7EptCZOnMiWLX8w9L/jERGvwzFFQJ06dejSpQsTJ04kNjaW++67jxMnTlC/fn06duzodXjGmFzK7mrir7l/R/rfcK9LlRUR6SYiv4jIRhEZHmB5pIhMd5cvFpE4n2UPu+W/iMhlblkjEVnpczsoIve4y0aIyHafZT1ytSdMsfLxxx9TrU4DGrW+0OtQTBEyZMgQtm7dyl133cWll15KrVq1+Oc//0lOTohqjClc8jIwIcsuKhEJBcYB3YGmwPUi0tSv2hBgn6o2AJ4HRrvrNgWuA5oB3YCXRSRUVX9R1Raq2gJoDRwFPvLZ3vPpy1V1Vh5emynCVJUlS5ZSp2lLa70xudK7d2+6dOnCE088wcyZM3nyySdZunQp77//vtehGWNyKS8JTnb/OdoAG1V1k3uhzneBXn51egGT3fszgM7i/EfqBbyrqsmq+juw0d2er87Ab6r6Rx5egymGNm3axL59e6nd8ByvQzFFTGRkJHPmzOHRRx8lJCSEgQMHcs455/Dwww9z/Pjx7DdgjCk08pLgZNdmWwPY6vN4m1sWsI6qpgAHgEo5XPc64B2/smEislpE3hSRCtm+AlMsLV26FIDajZt7HIkp6kJDQ3nqqafYtGkT48aN8zocY0wuZDeL6pA7zsX/dgjw7EQb7uUieuJc1TzdK0B9oAXwFzAmk3WHisgyEVm2a9euoMdqCt6SJUuIiIyiWp0GXodiioHu3bvTvXt3Hn30UbZs2eJ1OMaYHMpukHGMqpYNcItR1exmYG0Havk8rumWBawjImFAOWBPDtbtDiSq6t8+sf6tqqmqmga8weldWun1XlfVBFVNiI2NzeYlmKJo8ZIl1GjQlNCwcK9DMcWAiPDyyy+jqtxxxx024NiYIiKYZz9bCsSLSF23xeU6YKZfnZnAIPf+NcBcdY4eM4Hr3FlWdYF4YInPetfj1z0lImf5POwD/JRvr8QUGSkpKSQmJlLLxt+YfBQXF8eTTz7J559/bgOOjSkigpbguGNqhgFfAj8D76nqWhF5QkR6utUmAJVEZCPOrKzh7rprgfeAdcBs4E5VTQUQkTJAV+BDv6d8WkTWiMhq4BLg3mC9NlO47Nixg1WrVgGwdu1ako4do1YjS3BM/rr77rtp1aoV99xzD6mpqV6HY4zJRnbdTHniTtWe5Vf2mM/9JKBvJuuOAkYFKD+CMxDZv/yGvMZriqaHHnqI6dOnk5iYeHKAcSMbYGzyV1hYGPfccw833ngjP/30E+eee67XIRljsmAX6DFFXmLiCpKTk7nhhhtZuHAhZWLKUbl6ba/DMsXQxRdfDMB3333ncSTGmOxYgmOKtOPHj/PLL+up2aAZiYnLmTx5MjUbnmMn+DNBUadOHWrVqsWCBQu8DsUYkw1LcEyR9ssvv3DixAk6XHMz53Xtg6pS28bfmCC6+OKL+e6772w2lTGFXFDH4BgTbGvWrAHgrLoNadq2I6mpKbTseLnHUZni7KKLLuKtt95i48aNxMfHex2OMSYT1oJjirQ1a9YQGhpGlZp1KVUmhoHDn6VanP3TMcFj43CMKRqsBccUaatXr6Zq7XqEhUfkaTvhocKg9nHERDknB/zypx2s++vgKXXKRIbRuFoM1cpFUalMBKUjwogIC+F4Shp7jiSz4e/DrNl2gNRMui5qlC9F+waVqFo2irQ0Zdv+Y3y/YTd7j2R+jaN+CbWIjYlk8g+bOZyckqfXaPJH48aNqVy5MgsWLGDw4MFeh2OMyYQlOKZIW716DdXi8z5dt339yhnJTWZqlC/FxQ1Pnv06JTWNlNQ0SkWEUjOiNDUrlKZ5zXJ8kLidI37JSI3ypbi6dU1CQ4SU1DTCQkOoHxtNjfKleGfJFvYfPXHa8zWrXpYaFUrx3a+7LLkpRESEiy66yFpwjCnkLMExRdb+/fvZtm0r517aL0/bqRITSYta5flr/zHOKl8q03qHkk6w6Lc9bNt3lF2HkklOSQMgIjSEhtVi6NAwlkrRkXRrVo0PEredsu6F8ZUJDREWb9rDok17CBWhS9OqNDmrLO3qVWL2TztOqR8ZFsKF8ZXZcziZxC378vT6TP676KKL+Oijj9i+fTs1avhfB9gYUxjYGBxTZP30k3M1jrPqNszTdjo3qQrAN+t3ZlnvrwNJ/LhpD9v2HctIbgCOp6bx0/YDzP/FWb92pdJER5787RAWIlQrF8WR5BQW/bYHVUhJU+au30maKrUrlj7tuS6Kr0zpiDC3Tp5engmC9HE4X3/9tceRGGMyYwmOKbJWr14N5C3BaVmrPNXKRbFq2352HUrOUzx/H0zKuO+b4ESFhxIiwsFjJ/DNVY6npHHseCpR4aGnbKda2SjOrlGOn/86yLZ9x/IUkwmOc889l4YNG3LnnXdakmNMIWUJjilSkpOTOXToEODMoCodHUP52LOyWSuwMpFhnN+gEkeSU/jhtz15jq26T/fWwaSTY2qSTqSSpkrZUuH4nn4wIiyEUhGhJJ04eV0jATo1qcLxlDQW/LorzzGZ4AgLC2P+/PnUq1ePHj16MGPGDK9DMsb4sQTHFCn33nsv1atX58MPP2T1mjVUi2t4xmctvqRRLJFhoSz4dRfHfbqcciNEoGxUGK1ql88YgPzrjkMcPX4yaUlJU3YcSKJMZBjt6ldCBEJDhEsaVSFEhC17j2bUPbdWeaqWjeKH3/acsg1T+Jx11ll8++23tGnThgEDBrB9+3avQzLG+LBBxqbISEtLY8aMDzh69BhXX301oaGhtO1+ZgOM61UuQ3zVGLbuPcr6HYdyvf7NF8RRvvSpU9NVlV93HOKrdTtOq79w426ualWTdvUqkVCnAiJCaIiQnJLKj5uc1qPSEaG0r1+JnQeTWLV1/xm9LlOwKlSowNSpU2nQoAEvvvgio0eP9jokY4zLWnBMkZGYmMiuXTvpe88TnH/5taSmplK7ce6vGh4WIlzSuAqp7kDfM3H0eCpHklNOafn59e9DfL9xNydSTx8VvG3fMT5M3Mb2fUdRICUtjU27DjN96daMKeIdGsYSERbC3PU7UaBcqXCuPLc6d1xSnzsvaUDPc6tTrlTWU9lNwatbty7XXHMNr776KgcPHsx+BWNMgbAWHFNkzJo1CxGh2fmdaNvtGi7uM4jYmnVzvZ32DSpRtlQ4S3/fm+VJ9rIyfenWjPulI0JpVr0cbepWpH5sNLN/2sGGnYdPW2fbvmO8t2zbaeUAtSqUovFZZVmz/QB/HUiiVHgo155Xi9IRoWzadQQF6seWoVq5KKYu+oNjJ6z7qjB54IEHeO+99xg/fjz33Xef1+EYY7AWHFOEfP75LOo0PpfochUBqFq7PiEhufsIx0ZH0rJWBQ4eO5HRNZRXR4+nsnTzXr5Y8xdhoSFcdnY1ykTm/LdDiMAljatw7Hgq329wBhafV7cCZSLD+H7Dbmau+pNPV/3JD7/toUxkGOfVrZAvcZv8c95559GhQwdeeOEFTpw4/aSNxpiCZwmOKRJ27drF0qVLaHzexXnaTsfGsYSECD/8thsR5xINvrd0oSHO47CQnA9g3rT7CAeOnSA8NIRG1WJyvF7rOhWpFB3J9xt3k3TC6fKqWzmaNFVWbTs5Fmfllv2oKnUrR+d426bgPPjgg2zdupV33nnH61CMMVgXlSkivvzyS1SVJm065Gk76Zdj6HZ21lPLuzStSpemVTlw7ARvfv97jrd/JDmFcqXCKZ/DsTJlo8JoW68if+0/xk/bD5xSfux46injeY6npnH0eCplo+xrWxh1796dli1b8thjj9GvXz+ioqK8DsmYEs1acEyRMGvWLMpWrEyNBk29DiVLZd0E6kRqzqadd2xchVCRgGdRDtR6FBZ6ZlPiTfCFhITw7LPP8scff/Diiy96HY4xJV5QfwqKSDfg/4BQYLyqPuW3PBKYArQG9gDXqupmd9nDwBAgFbhbVb90yzcDh9zyFFVNcMsrAtOBOGAz0E9V7SI+xUBqaipfzJ5No/MuyfWYG3/Ztcbc29U5K3Kgq4mLQCYXCgegcbUYot3Wle05OANxvdgy1I+NZsWWfaedRflgUgoVy0RQrlQ4B445YzoqlA4nMiyUPYfzdsZlEzydOnXiiiuuYNSoUQwePJjKlSt7HZIxJVbQWnBEJBQYB3QHmgLXi4j/z+8hwD5VbQA8D4x2120KXAc0A7oBL7vbS3eJqrZIT25cw4FvVDUe+MZ9bIqBuXPnsn/fPpq27ehpHP0SanFeXAUqlok45YzEMVFhtKtXka7NnGta/X0wiU27j2S5rdAQoWOjKpmeRXmzu37HRrEZ44M6NKriLNuT9baNt55++mmOHDnCyJEjvQ7FmBItmC04bYCNqroJQETeBXoB63zq9AJGuPdnAGPFOS1tL+BdVU0GfheRje72FmXxfL2Aju79ycB84KH8eCHGW1OmTKF0TFnPE5wykWFcGB/LhfGxpKYpx1PSCA0RIsJO/k7YcSCJT1Zmf0bbdvUqUq5UOF+s+SvgWZSXbt5Lo2ox1IuN5vaODQAnKTqcnMLS361hsjBr0qQJt912G2PHjqVy5co89thjZ3y2bWPMmQtmglMD2OrzeBvQNrM6qpoiIgeASm75j37r1nDvK/CViCjwmqq+7pZXVdW/3Ps7gKqBghKRocBQgNq1a5/ByzIF6dChQ3zw4Ye0uORKwiMiPY3lq7U7iKtUhhoVShETFUap8FAUOHDsBDsPJvHr34fZ8Pchsrv4d4XS4bSqUyHLsygfPZ7Ke8u2cnHDWGpVcK42vnn3Eb79dZedA6cIeO655zh8+DAjRoxg3bp1vPHGG5QtW9brsIwpUYridIwLVXW7iFQB5ojIelVd4FtBVdVNgE7jJkSvAyQkJGT3v8h47MMPP+TY0aMkdOldIM/3/JxfM122bd+xfLm6976jJ3jpm43Z1tt/9AQzV/6Z5+czBS8yMpJJkybRtGlTHn74YWbOnMmVV17JoEGD6NGjh7XoGFMAgjmLajtQy+dxTbcsYB0RCQPK4Qw2znRdVU3/uxP4CKfrCuBvETnL3dZZwJmdg98UKpOnTCG2em3imrb0OhRjckVEeOihh1iyZAm33HIL8+fP54orrqBjx44kJiZ6HZ4xxV4wE5ylQLyI1BWRCJxBwzP96swEBrn3rwHmqqq65deJSKSI1AXigSUiUkZEYgBEpAxwKfBTgG0NAj4J0usyBWTLli3MnzePVl162S9eU2QlJCTw0ksv8eeff/Lqq6+ybt06EhISmDBhgtehGVOsBS3BUdUUYBjwJfAz8J6qrhWRJ0Skp1ttAlDJHUR8H+7MJ1VdC7yHMyB5NnCnqqbijKv5XkRWAUuAz1V1trutp4CuIrIB6OI+NkXY1KlTUVUSOvfyOhRj8iwsLIzbbruNDRs2cPHFF3P//ffz999/ex2WMcWWaFYn9ijmEhISdNmyZV6HYQI4cuQIdevWo2LteG7735teh1OkNKgSzZXnVvc6DJOF9evX07x5c/r378+kSZO8DseYIktElvudMiaDncnYFEovv/wyu3bt5NKBw7wOxZh817hxY+677z4mT57MwoULvQ7HmGLJEhxT6Bw6dIinRo+m8XkXUbdZK6/DMSYo/v3vf1OzZk2GDRtGSW5JNyZYLMExhc7YsWPZu2cPl91wt9ehGBM00dHRjBw5kpUrV/LDDz94HY4xxY4lOKZQOXToEM88+yxN23akTuPmXodjTFD17duX0qVLM2XKFK9DMabYsQTHFCqvvvoq+/bu5dKBd3odijFBFxMTw1VXXcX06dNJSkryOhxjihVLcEyhkZSUxLPPjqFRqwuo3chab0zJcOONN3LgwAE+/fRTr0MxplixBMcUGhMnTmTnzr/pfP1tXodiTIHp1KkTNWrUr4fHJgAAGTdJREFUsG4qY/KZJTimUEhJSWH06KeJa9KC+s3bZL+CMcVEaGgoAwcO5IsvvrAT/xmTjyzBMYXCtGnT+OOPzXS+7ja7LIMpcW688UZSU1OpWbMmUVFRNGvWjKNHj3odljFFmiU4xnOzZ8/m9ttvp06j5jRp29HrcIwpcE2bNuW1117j/vvvZ8iQIaxbt45XX301Y/nOnTuZN2+ehxEaU/TYpRrsUg2e+vjjj+nXrx9V68Qz9H8TiC5X0euQijy7VEPR17VrV1atWsXvv/9OWFgY7du3JzExkcTERFq2bOl1eMYUGnapBlMozZs3j759+1KjQVP+3+hJltwY4xo5ciS7du1i3LhxDB8+nMTEREqVKsWjjz7qdWjGFBlhXgdgSqbff/+da67pS+Uadbj1vxMoVSbG65CMKTTat29Pt27deOKJJzhy5Ah33XUX1atX5+GHH2bRokWcf/75XodoTKFnLTimwB0+fJiePXuRdOIEN4942ZIbYwIYOXIkR44coWXLljzzzDPcddddVK1alUceecTr0IwpEizBMQXu/vvvZ926tdzw8PPE1ojzOhxjCqU2bdrwxRdfMGvWLCIjIylTpgz/+te/mDdvHl999ZXX4RlT6FmCYwrUb7/9xoQJE7ig5wAaJVzodTjGFGrdunWjWrVqGY+HDh1KfHw8AwcOZPPmzd4FZkwRYAmOKVBPPvkkIWH/v717j66quhM4/v0lgQTDIyEESGIgWKAQobzLw1awUEBGCDpSsFp8jagDFbR2FDujljW1SkeRPihodRCkBQQcQqcMD8Gxa1UEIhIkFg0PkRhDghC9IeRx85s/zglzwSQkmOTk3vv7rHVXzt1nn3N/m31z+eWcffeOYuz0WV6HYkzQiYmJITMzk4qKCm644Qa++OILr0MypsWyBMc0mw8//JCVK1cy6oYf0j6hs9fhGBOU+vTpw7p16zh06BDTpk3D5/N5HZIxLVKTJjgiMlFEDolIrog8WsP+aBFZ4+5/R0TSAvbNd8sPicgEtyxVRHaKSI6IHBSRuQH1nxSRPBF5z31Masq2mYZbsGABrVpH870f/JPXoRgT1MaOHcsLL7zA9u3bGTlyJB999JHXIRnT4jRZgiMikcDvgOuBdOAWEUm/qNrdwGlV7QksAp5xj00HZgBXAxOBJe75KoGfqGo6MAKYfdE5F6nqQPfxl6Zqm6m/iooK1q5dy7hx32fVqlWMmvxD2sV38josY4LenXfeyZYtW8jPz2fYsGHs2LHD65CMaVGa8grOt4FcVT2iquXAaiDjojoZwCvu9jpgrDgLEWUAq1W1TFWPArnAt1U1X1XfBVDVL4EPgJQmbIP5mmbNmsX06dPZ934OE2c+wISZD3gdkjEhY9y4cWRlZZGamsqUKVPYs2eP1yEZ02I0ZYKTAnwS8PwEX01GztdR1UqgGEioz7Hu7axBwDsBxXNEJFtEXhaR+JqCEpFZIrJXRPYWFhY2tE2mAXJzc1mxYgXfybiNx5ZvZ/xts2kdHeN1WMaElO7du7N161YSExOZNGkShw4d8jokY1qEoBxkLCJtgfXAPFWt/hrB74FvAAOBfODZmo5V1RdUdaiqDk1MTGyWeMPVr371KyKjWjHulvuIiIz0OhxjQlZSUhJbt25FRLjmmmu4//772bx5M36/3+vQjPFMUyY4eUBqwPMr3bIa64hIFNABOFXXsSLSCie5WaWqG6orqGqBqvpVtQp4EecWmfFIXl4ey5cvZ9j4m2jf0RJJY5par169eOONNxg9ejQrV65k0qRJPPzww16HZYxnmjLB2QP0EpEeItIaZ9Bw5kV1MoHb3e2bgR3qLG+eCcxwv2XVA+gF7HbH57wEfKCqzwWeSESSAp7eCLzf6C0y9fbcc89R6fdz3bS7vQ7FmLDRv39/1q9fT1FREXfddRe/+c1vOHDggNdhGeOJJktw3DE1c4AtOIOB16rqQRFZICJT3GovAQkikgs8BDzqHnsQWAvkAP8DzFZVP3AN8CPgezV8HXyhiBwQkWzgOuDBpmqbqVt+fj5Lly1j0Jh/ICEp9dIHGGMaVUxMDAsXLqRDhw7MnTsX5+9GY8KLhPMbf+jQobp3716vwwgpqsqUKRls2baVh5dm2lpTHujZuS2TByR7HYZpAZYsWcLs2bNZvXo1ffr0Yd++fUyYMIGkpKRLH2xMEBCRLFUdWtO+qOYOxoS2V199lT//eRMZ98635MYYj82aNYtly5YxY8aM82VTp07l9ddf9zAqY5qHJTim0eTl5fHjBx7gqn5D+O7UH3kdjjFhLyoqipUrV7JixQoGDx7Mnj17eP7559m/fz8DBgzwOjxjmpTdorJbVI3i3Xff5Yc/vJWjx47xk6WZJKZ09zqksGW3qExtzpw5Q1paGmPHjmX9+vUX7CspKaGwsJC0tDRvgjPmMtR1iyoo58ExLUdlZSVPP/00I0aMoODUae5asNSSG2NaqLi4OObOncuGDRvIzs4GoKqqiuXLl9OrVy/69OlDXt7Fs3kYE5wswTGX7e2332bw4CHMnz+f9BHf4ydLM+k9aKTXYRlj6jBv3jzat2/Pvffey+23307fvn258847SU5OpqKigiVLlngdojGNwhIcc0kff/wxa9asITs7m+LiYtatW8eNN97IqFGjOPHZSe54/DfM/NfFxLaP8zpUY8wlxMfH89Of/pRdu3axbds20tLSWLVqFbt37yYjI4OlS5dy9uxZr8M05muzMTg2BqdOubm5jBp1DYWFJy8o79AxkWET/pGxM2YR3SbWo+hMTWwMjrkUVcXn89GuXbsLyt966y1Gjx7NsmXLmDVrlkfRGVN/dY3BsQTHEpxa5efnM2rUNRSdPsNt85+jpPg0hXnHSEsfRM8Bw219qRbKEhxzuVSVIUOGcO7cOQ4ePMjRo0f5/PPPGTq0xv8/jPGczYNjGuzcuXNMnHg9+QUF3PfMK3Tv8y2vQzLGNDER4cEHH2TmzJn06tWLw4cPAzBt2jR++9vf0rlzZ48jNKb+bAyOqdHatWvJzt7PLT9daMmNMWFk+vTpjBgxgtTUVBYtWsSCBQvYuHEj6enp/PKXv+TYsWNeh2hMvdgtKrtFVaORI0dxJO8zHvnDZpw1Tk2wsFtUprHl5OQwZ84cdu7cCcDw4cMZP348Y8eOZeDAgXTo0MHjCE24snlwTIPs37+fXbveZuSkGZbcGGNIT09nx44dHDlyhKeeegpV5Re/+AVjxowhLi6Ojh07ct1117Fx40aqqqq8DtcYwBIcgzML8bhx49i0aRMAS5cupVV0NEO/P9XjyIwxLUmPHj2YP38+77zzDqdOnSIzM5OFCxcyY8YMjh8/ztSpUxk4cCAPPfQQzzzzDBs2bKCiosLrsE2YskHGYe6VV17hvvvuo6ysjDfffJPFixez8tVXGXjtJJvXxhhTq7i4OCZPnszkyZMBZ1bz1atXs2jRIpYtW3Z+Lp20tDQeeeQRRo8eTdu2benUqRNt2rTxMnQTJuwKTpgqLi7m7rvv5o477iC170AeW76NbwwYzpw5cyjx+Rh5w4xLn8QYY1xRUVHcdtttZGVlUVJSgs/nIzMzky5dunD//feTnp5Ot27d6Ny5My+//DLhPP7TNA8bZBxmg4xLSkrYunUrc+b8mM8+y+d702cxYeaPiYyMorK8nLWL/41zvi+488klNv4mSNkgY9OSqCq7du3i448/xufzsWrVKt58802mTp3KE088Qb9+/YiKuvBmQmVlJUVFRXTt2tWjqE2wsIn+ahEuCU5xcTFPPfUU69dv4MiRw6gqyT1684OHfkG3b9pXwEONJTimJauqqmLRokU89thjlJeXExsby+jRo1mwYAFDhgzh8OHDTJ8+naysLEaPHs28efMYP348V1xxRZ3nLSkpoU2bNkRE2I2JcGIJTi1CMcEpKCjg6aefJicnh/T0dBISEli8+NecOlVE+vAxXNm7H8k9+tB32LVEtW7tdbimCViCY4LBiRMneOutt9i1axerV6+mqKiIm266iW3bthEREcE999zDmjVrOH78OABJSUn079+fe++9l4yMDACysrLYvHkzmzdvZvfu3fTp04fHH3+cadOmEWkzrYcFS3BqEcwJTlVVFdnZ2Wzfvp1PPvmE2NhYfD4ff3jpJcrKykhK60XhiWOUl53jG98aRsa987my19Veh22agSU4JthUX2V+/vnnGTRoEGvWrKF79+5UVlayefNm9u/fz9GjR9m5cydHjx4lNTWV0tJSioqKEBGGDx/OmDFj2LRpEwcPHiQ9PZ0nnniCm2++mYiICMrLy8nLy6NNmza0bduWtm3bet1k00g8S3BEZCKwGIgE/qCqT1+0PxpYAQwBTgHTVfWYu28+cDfgBx5Q1S11nVNEegCrgQQgC/iRqpbXFV9LTHDOnTtHcXExPp+P7OxsNm7cyBtv7CAmJoauXbsQFRVFQcFJ8j7N44viYgDaxLaj/FwpVVV+Bo6exISZP6bzlT2o8vvxnTlFu46JNp4mjFiCY4LV6dOnad++fa1XX/x+P5s2beLFF1+kY8eOTJo0ifHjx5OQkAA4f/itW7eOn//85+Tk5HD11VfToUMHsrKyKCsrO3+eAQMGcM8993DrrbcSF+d8W7T6m6QHDhwgOTmZbt260a1bN5KTk78yRsi0HJ4kOCISCXwIfB84AewBblHVnIA6/wx8S1XvE5EZwI2qOl1E0oE/Ad8GkoHtQG/3sBrPKSJrgQ2qulpElgL7VfX3dcXYv39//dvf/nY+my8tLUVVz9/HPXv2LCdPnqS0tJSYmBiio6Mv+Fn9S6iqVFRUUFpaSmlpKWfPnq1zu/pneXk5Xbt25aqrruLTTz/lj3/8I9u2baOysvJ8jFe0a0+vQaMQicB3pgh/ZSVt4zvRPr4T3fsOoOfAkcR16gJAld9vC2AaS3BM2PP7/bz22ms8++yzREdHM2LECNLT0ykrK+Pzzz9n/fr17Nu3D4Dk5GRSU1PJycnhyy+//Mq5IiIiSElJoVu3bqSmptKxY0diY2OJjIyksLCQwsJCYmNjufLKK88/UlJSqKio4OTJk5w+fRpw/p84c+YMBQUFnDt3jr59+9KvXz8iIiIoKCjA5/ORmJhIYmIiZWVlFBQUcPbsWbp3705aWholJSUcPXqUoqIiEhIS6NKly/mv20dFRZGYmEh8fPxX/phVVfx+P36/n6qqqvPbpaWllJSUUFlZSadOnYiPj2/08UvV+UXgz9q2L2c/QFxcnCcJzkjgSVWd4D6f7wb1y4A6W9w6b4tIFPAZkAg8Gli3up572FfOCTwNFAJdVbXy4teuI0YFaN26NRUVFRd8bbF169aUl9d5AYjIyMjz9fx+/6X+SS4psWsKw8b+A4lJqUS3aUOnrin0HjCUqKhWX/vcJnykxl/Btb0TvQ7DmBatevzOkSNHOHbsGD179iQjI4ORI0dy8uRJjh8/XuPjzJkz+Hw+/H7/+YSkpKSEEydOXHCVqDbR0dG0atUKn8/X6G2KioqiVatWFyQz9f0/Pioqiujo6EZLRpqRJ6uJpwCfBDw/AQyvrY6bmBTj3GJKAXZddGyKu13TOROAM6paWUP9C4jILGCW+9QHHHITmU5AUXW9SyU3wPksuLEUfpbHX1a90Gjnu4QL2hsGwq29EH5ttvaGtiZt786dO3nxxRcbdEx+fj75+fkNOqasrKxeiRCX0d7KysoL7gA017GN6HL6uHttO8LuxqKqvgB8JYsQkb21ZYGhyNob+sKtzdbe0GbtDX2N3eamnDAgD0gNeH6lW1ZjHfcWVQecwca1HVtb+Skgzj1Hba9ljDHGmDDRlAnOHqCXiPQQkdbADCDzojqZwO3u9s3ADnVu4GUCM0Qk2v12VC9gd23ndI/Z6Z4D95wbm7BtxhhjjGnBmuwWlTumZg6wBecr3S+r6kERWQDsVdVM4CVgpYjkAp/jJCy49dYCOUAlMFtV/QA1ndN9yUeA1SLy78A+99wN0WyDX1oIa2/oC7c2W3tDm7U39DVqm8N6oj9jjDHGhCZbtMMYY4wxIccSHGOMMcaEnLBLcERkjYi85z6Oich7bnmaiJQG7FsacMwQETkgIrki8msJsnUPRORJEckLaNukgH3z3XYdEpEJAeUT3bJcEXnUm8gvj4j8SkT+LiLZIvK6iMS55SHbx4GCue9qIyKpIrJTRHJE5KCIzHXLG/zeDhbu59MBt1173bKOIrJNRD5yf8a75eK+b3Pd9/1gb6NvGBH5ZkAfviciX4jIvFDrXxF5WUROisj7AWUN7lMRud2t/5GI3F7Ta7UEtbS3+T6fq2cgDMcH8CzwuLudBrxfS73dwAhAgM3A9V7H3sB2Pgk8XEN5OrAfiAZ6AIdxBm9HuttXAa3dOulet6MB7R0PRLnbzwDPhHofB7QjqPuujnYlAYPd7XY4S7akN/S97XU7GtjmY0Cni8oWAo+6248GvLcnue9bcd/H73gd/9dodyTOrPbdQ61/gWuBwYGfQw3tU6AjcMT9Ge9ux3vdtga0t9k+n8PuCk41NwP8Ac6aV3XVSwLaq+oudf6lVwBTmyHE5pABrFbVMlU9CuTirP/1bSBXVY+os2DparduUFDVrfr/s1rvwpkXqVYh1sdB3Xe1UdV8VX3X3f4S+IBaZit31fbeDnYZwCvu9iv8//s0A1ihjl0484IleRFgIxgLHFbVj+uoE5T9q6pv4XxjOFBD+3QCsE1VP1fV08A2YGLTR99wNbW3OT+fwzbBAb4LFKjqRwFlPURkn4j8r4h81y1LwVn6oVqty0C0cHPcS4IvV18CpeblNFLqKA9Gd+Fk/NVCuY8htPquRiKSBgwC3nGLGvLeDiYKbBWRLHGWmAHooqrVawN8BnRxt0OhvdVmcOEfnqHav9Ua2qeh1PYm/XwOyQRHRLaLyPs1PAL/kr2FC3+J8oFuqjoIeAj4o4i0b864v45LtPn3wDeAgTjtfNbTYBtBffpYRH6GM4/SKrcoqPvYgIi0BdYD81T1C0LwvR3gO6o6GLgemC0i1wbudP+aDal5PsSZwHUK8JpbFMr9+xWh2Ke1aY7P55Bci0pVx9W1X5wlHW4ChgQcUwaUudtZInIY6I2z5EPgJbQWuQzEpdpcTUReBP7sPq1rOY1LLbPhqXr08R3ADcBY90Mj6Pu4nuqzREpQEpFWOMnNKlXdAKCqBQH76/veDgqqmuf+PCkir+PcgikQkSRVzXcv3Z90qwd9e13XA+9W92so92+AhvZpHjDmovI3myHORtNcn88heQWnHsYBf1fV85e9RCRRRCLd7atwloc44l46/EJERrjjdmYSZMtAXHQv/kagekR7g5bEaM6Yvw4RmQj8CzBFVc8GlIdsHwcI6r6rjdsvLwEfqOpzAeUNfW8HBRGJFZF21ds4AzPf58LlbQKXpMkEZopjBFAccNsjmFxwZT1U+/ciDe3TLcB4EYl3b9mNd8uCQrN+PjfniOqW8gCWA/ddVPaPwEHgPeBdYHLAvqE4v1iHgd/izgAdLA9gJXAAyMb5pUkK2Pczt12HCBiZjjOC/0N338+8bkMD25uLc4/6PfexNNT7+KL2B23f1dGm7+Bcus8O6NdJl/PeDoYHzrfg9ruPg9X9CCQAbwAfAduBjm65AL9z23sAGOp1Gy6jzbE4Cyd3CCgLqf7FSd7ygQqcsSR3X06f4oxdyXUfd3rdrga2t9k+n22pBmOMMcaEnHC9RWWMMcaYEGYJjjHGGGNCjiU4xhhjjAk5luAYY4wxJuRYgmOMMcaYkGMJjjHGcyLiF2cF4fdF5DURuaKBx//Jnc7/QRFZICLj3PJ5dZ1LRNa5c27Utv9NERnqbm8PWCrAGNPCWYJjjGkJSlV1oKr2A8qB+wJ3urOP10hEugLDVPVbqrpIVR9X1e3u7nlAjQmOiFyNswL1kXrGuBL453rWNcZ4zBIcY0xL81egp4iMEZG/ikgmkCMiMSLynyJywF2Q7zq3/lYgxb0C9F0RWS4iN4vIA0AysFNEdtbwOrfizogqIlPc498TkUMicjSgXvUVnkycmXaNMUHAEhxjTIvhXqm5HmfmVoDBwFxV7Q3MxlmPsD9OovGKiMTgLM542L0C9Nfqc6nqr4FPgetU9Tq+6hogy62b6R4/EGf24P9w67TBXRJAVU8D0SKS0MjNNsY0AUtwjDEtQRsReQ/YCxzHWXcKYLeqVl9N+Q7wKoCq/h34GGcxvsuVBBQGFojIv+DcLvudiMzFuXrzlDoLAYKzEGLy13hNY0wzCcnVxI0xQafUvXpynrOuHiVN+ZpATMDrjQOmAdcCqOpiEbnxomNi3OOMMS2cXcExxgSLv+KMm0FEegPdcBZarMuXQLta9n0A9HTP1x1nYcNpqlpjAuOuZNwVONbQwI0xzc8SHGNMsFgCRIjIAWANcEfAraPavAD8Ty2DjP8bGONu34GzqvN/uQON/1JD/SHALlWtvJzgjTHNy1YTN8aEJRFpA+wErlFVfz3qLwYyVfWNJg/OGPO12RUcY0xYcm9FPQGk1POQ9y25MSZ42BUcY4wxxoQcu4JjjDHGmJBjCY4xxhhjQo4lOMYYY4wJOZbgGGOMMSbkWIJjjDHGmJDzf2jR4RXt96RuAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["width = 800\n", "height = 400\n", "dpi = 100\n", "\n", "loss_prob = int(100 - np.sum(X > 0) / X.shape[0] * 100)\n", "mask = bins[1:] <= 0\n", "\n", "plt.figure(figsize=(width/dpi, height/dpi))\n", "plt.plot(bins[1:], X_unity, c='black')\n", "plt.fill_between(x=bins[1:][mask], y1=0, y2=X_unity[mask], alpha=1/2)\n", "plt.ylabel('Likelihood')\n", "plt.xlabel('Profit (zł)')\n", "plt.text(-250, 0.005, s=f'{loss_prob}%', color='white', fontsize=24)\n", "plt.ylim(0)\n", "plt.xlim(np.min(bins[1:]), np.max(bins[1:]))\n", "plt.title('Probability of loss over the next 100 days.', fontsize=18)\n", "plt.tight_layout()\n", "plt.savefig('./img/probability_of_loss.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### CDF from simulation\n", "\n", "I use the data from the simulation to create a cdf."]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAARUAAADQCAYAAAApvPVRAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAbeElEQVR4nO3de3xV1Znw8d+TKxDu91si9yIXuRhQi6IWRGBasBcFtPNS68g4rVOtfTsfrdU6Oq/z2pnW6lvslI5UyrQg4OhERREKFlARAiI3uYQAuUBIuIVA7snz/rF38DQGck5ysvc5Oc/38zmf7LP2yj7PJuHJ2mvvtZaoKsYYEy5xfgdgjGldLKkYY8LKkooxJqwsqRhjwsqSijEmrCypGGPCKsHvAJqje/fuOmDAAL/DMCbmbN++/ZSq9mhoX1QnlQEDBpCZmel3GMbEHBE5drl9dvljjAkrSyrGmLDyJKmIyGIRKRSRPZfZLyLyoohkicguERnvRVzGmPDzqqXyCjD9CvtnAEPd1wLgNx7EZIxpAZ501KrqRhEZcIUqs4E/qDO6cYuIdBaRPqp6wov4jGmMqlJRXcuFimouVlRTUu58vVhZTWllDVU1tVRVK5U1tc52TS1VNUptrVKrUKOKqlLjvg/crnX3AShQN8ZX0YDtz8txyxuq11xdUpL4ycyrm3WMSLn70w/IDXif55Z9IamIyAKc1gxpaWmeBGdat/PlVeScLuXY6VJyzpRSUFxG0YUKikqc17myKi6UV1Nd27z/uXECcSLExcml7XgRREDcrwCC8/7z7boj1K8DgltPQGi+Xp3aNPsYkZJUgqaqi4BFAOnp6TZvgwlaTa1yuOgCu/KK2ZNfzO78YrKLLnC2tOqv6nVqm0iPDsn0aJ/M6P6d6dIukfbJCaQkJ9ChTQIpSQHbyQm0TYwnKSGOpIQ4EuOFpPg4EuPjiI8T5xWQOGJBpCSVfCA14H1/t8yYZjl66iJr953kg8OnyDx6lgsV1QC0S4pnVN9OzBzdh7Su7biqWzvSuqaQ1q0d7ZMj5b9FdIqUf70M4EERWQ5cBxRbf4ppqvxzZazMzOXdPQXsLygBYHCPFGaP7cu1V3Xhmv6dGNi9PfFxsdFy8JonSUVElgG3AN1FJA/4GZAIoKr/AawGZgJZQClwrxdxmdZDVdl46BRLPzrG+v0nUWDCgK488dURTBvRi9Su7fwOMWZ4dfdnXiP7Ffi+F7GY1kVVWb+/kF+tO8Tu/GK6pSTxwM2DmTcxzRKJTyLl8seYkH2cfZpnV3/Gp3nFpHZty3PfHM0d4/qRnBDvd2gxzZKKiTqF58t56s29rN5dQN9Obfj5N6/h6+P7kRhvo04igSUVE1Xe3VPAY/+9i9LKGh65bRgLJg+iTaK1TCKJJRUTFS5UVPP0m3tZkZnHqH4d+dWccQzp2d7vsEwDLKmYiLf3eDHf++MOcs6U8v1bB/PQlGEkJdilTqSypGIi2rp9J/nB8k/o1DaRVxfcwMSBXf0OyTTCkoqJSKrK4g+O8i9v72NU3068PD+dnh2bPy7FtDxLKiYiPb/2IC+uz+L2kb14fs5Y2iXZr2q0sJ+UiTi//cthXlyfxZz0VP71G6OJs8fpo4r1dpmIsmJbLv/6zn6+ek0fnrWEEpUsqZiIselQEY+9vpvJw3rw/JyxNuAvSllSMRHhQEEJ3/uvHQzt2Z6Fd4+zp2OjmP3kjO8KS8r57ivbaJsUz+LvTKBDm0S/QzLNYB21xlfVNbU8+MdPOHOxkpUP3EDfzm39Dsk0kyUV46vn1x1k69Ez/GrOWEb16+R3OCYM7PLH+GbjwSIWbjjM3Amp3DGun9/hmDCxpGJ8cb68in9atYuhPdvzs6+N9DscE0aWVIwvnn37MwpLyvn3O8fQNsmmLmhNLKkYz206VMTybbncP3kQY1I7+x2OCTNLKsZTFdU1PPHGHgZ1T+GHU4f5HY5pAZ4lFRGZLiIH3EXYH21gf5qIbBCRT9xF2md6FZvxzu82ZnP0dCn/PHukzdjWSnmSVEQkHliIsxD7CGCeiIyoV+2nwApVHQfMBV7yIjbjnbyzpfx6QxYzRvXmpqE9/A7HtBCvWioTgSxVzVbVSmA5zqLsgRTo6G53Ao57FJvxyDNv7UMQfvrV+n9PTGviVVK53ALsgZ4Cvu0uNrYa+EdvQjNe+MvBItbsPcmDXxlCP3tqtlWLpI7aecArqtofZ7XCpSLyhfhEZIGIZIpIZlFRkedBmtBVVNfwVMZeBnZP4e9uGuh3OKaFeZVUglmA/T5gBYCqfgS0AbrXP5CqLlLVdFVN79HDrsujwcubj3Dk1EWemjXSFvqKAV4llW3AUBEZKCJJOB2xGfXq5ABTAETkapykYk2RKHf6QgUvbTjM1Kt7cfMw+yMQCzxJKqpaDTwIrAE+w7nLs1dEnhaRWW61HwH3i8inwDLgO+4ayyaK/b/1WZRWVvPojC/5HYrxiGejlFV1NU4HbGDZkwHb+4BJXsVjWl7O6VL++PEx5kxIZUjPDn6HYzwSSR21ppX5xdoDxMcJD9uTszHFkoppEdlFF3jz0+PMv2EAvWy9nphiScW0iIUbDpOUEMf9kwf5HYrxmCUVE3Y5p0t5Y2c+d0+8iu7tk/0Ox3jMkooJu5fezyI+Tvj7m62VEossqZiwyj9Xxms78piTnmp9KTHKkooJq/94/zAAD9wy2OdIjF8sqZiwOXm+nFczc/nWtf1t0GAMs6Riwua3f8mmplb5h5uH+B2K8ZElFRMWxWVVLN+Ww+wxfUnr1s7vcIyPLKmYsFi+NYfSyhrus6kNYp4lFdNs1TW1LPnwKNcP6srIvrbKYKyzpGKa7Z09BRwvLue+G+25FGNJxYTBy5uPMKBbO6YM7+l3KCYCWFIxzbIj5yw7c89x76SBxMWJ3+GYCGBJxTTLy5uP0LFNAt+6tr/foZgIYUnFNFn+uTLe3VPAvIlppCR7Nt+XiXCWVEyTLfnwKADzvzzA1zhMZLGkYpqkvKqG5VtzmD6qN33tkXwTwJKKaZLVu09wvryae65L8zsUE2EsqZgmWb41lwHd2nHDoG5+h2IijGdJRUSmi8gBEckSkUcvU+cuEdknIntF5E9exWZCk1VYwtajZ5g7MQ0Ru41s/lrIXfYikgKUq2pNCN8TDywEbsNZR3mbiGS4y3LU1RkKPAZMUtWzImJPUkWo5VtzSYwXu41sGtRoS0VE4kTkbhF5W0QKgf3ACbdF8W8iEsw494lAlqpmq2olsByYXa/O/cBCVT0LoKqFoZ2K8UJFdQ2v7cjjthG9bP5Z06BgLn82AIOBR4Heqpqqqj2BG4EtwHMi8u1GjtEPyA14n+eWBRoGDBORD0Rki4hMb+hAtkC7v9bsPcnZ0irmTbQOWtOwYC5/pqpqlYi8oKoP1RWq6hngNeA1EUkMUyxDgVtwFnDfKCKjVfVcYCVVXQQsAkhPT7dlUT227OMcUru2ZdLg7n6HYiJUoy0VVa1yN0tE5E23TwURuV1EPqhX53LygdSA9/3dskB5QIaqVqnqEeAgTpIxEeLIqYt8lH2auRPSbJyPuayg7/6o6k9xFk5/300mj+BcEgVjGzBURAaKSBIwF8ioV+cNnFYKItId53IoO9j4TMtbvi2H+DjhTuugNVcQ9N0fEZmC05l6EegDfFdVDwTzvapaLSIPAmuAeGCxqu4VkaeBTFXNcPdNE5F9QA3wY1U9HdrpmJZSWV3La9vzmDK8Jz1t6Q1zBaHcUn4ceEJVN4vIaOBVEXlEVdcH882quhpYXa/syYBtxWn9PBJCTMYj6z47yakLldZBaxoVdFJR1a8EbO8WkRk4HbVfbonATGRZtjWHvp3aMHlYD79DMRGuyU/UquoJYEoYYzERKvdMKZuzTnHXhFTirYPWNKJZj+mralm4AjGR69VtuQhwV3pqo3WNsQGF5oqqa2pZuT2XW77U06Y4MEEJOqm4AwKT3O3+IjJORL7WcqGZSLDp0ClOnq+wVooJWigtlZnAre72/TjPkfyfsEdkIsrK7bl0TUniKzZTvglSKEnlHZzEAjANeA+wXrtW7OzFStbtK+SOsf1ISrArZROcUH5TNgC3ulMSVNeNJjatV8anx6msqbUpDkxIQnlMvxxnfM7DOK0W08qt2p7HyL4dGdG3o9+hmCgSapv2HZykUvdkrI0SbqX2F5xnd36xtVJMyEJNKquBg6q6031/MszxmAixKjOPxHhh9tj6094Yc2UhTSepqoeBsQHvbwt7RMZ3VTW1vLEznynDe9E1JcnvcEyUsS598wUb9hdy6kIld6bbpY8JnSUV8wWrtufRvX0yN9vgQdMEwUx8vaTuSVrT+p26UMH6/YV8Y3w/EuLtb44JXTC/NbnARyIyILBQRK4RkcUtEZTxz//sPE51rdpdH9NkjXbUqupPRWQLsE5EHgIScW4rdwBeaOH4jIdUlZWZuYzp34lhvTr4HY6JUsHe/dkIvAu8CRQCd6nqxhaLyvhi7/Hz7C8o4ZnZI/0OxUSxYPpUXgJ2AxeAq4H1wA9EpF0Lx2Y8tjIzl6SEOGaNsWdTTNMF06fyKTBcVR9V1QOqejfwEbBFRIa1bHjGK+VVNbyx8zjTR/amU7twLONkYlUwlz+L3EmpL1HVX4jIJzhP2A4REalfx0SXtftOUlxWZfOmmGYLatlTEflHEak/jfpm4J9FZAkwv7GDuJM8HRCRLBG57HpBIvJNEVERSQ8iNhMmKzJz6de5LV8e3M3vUEyUCyapTMdZh2eZiNQtzH4EOARMBn6lqq9c6QAiEg8sBGYAI4B5IjKigXodgIeAj0M6C9Msx8+VsTnrFN+8tr+tPGiaLZhbyuXAS8BL7prJ3YGy+mscN2IikKWq2QAishyYDeyrV+8Z4DngxyEc2zTTa9vzUMVWHjRhEczdn/kickpEzgD/CVwIMaEA9MN5iK5OnlsW+DnjgVRVfbuReBaISKaIZBYVFYUYhqmvtlZZuT2PLw/uRmpXu6Fnmi+Yy58ngNuA4UAO8Gy4gxCROOCXwI8aq6uqi1Q1XVXTe/SwsSnNtfXoGXLOlFoHrQmbYO7+nFfVT9ztJ0SkKf0d+UDgb21/t6xOB2AUzuLvAL2BDBGZpaqZTfg8E6QVmbl0SE7g9pG9/Q7FtBLBtFT6uJcck0WkB85j+qHaBgwVkYHu4MS5QEbdTlUtVtXuqjpAVQcAWwBLKC2spLyK1btP8LWxfWmbFO93OKaVCKal8jNgNHCP+7W9iKzGeShul6oua+wAqlotIg8Ca4B4YLGq7hWRp4FMVc248hFMS3hr1wnKq2rt0seEVTB3fxYFvheR/jjJ5RqcJTsaTSrucVbz+dy2dWVPXqbuLcEc0zTPysxchvVqz5j+nfwOxbQiIU0nCaCqeTh3b2xG/SiWVVjCjpxzPD7zatx+LGPCwmbhiVHLtuaSGC98fbwNHjThZUklBpVX1fDajjymjexN9/bJfodjWhlLKjFozd4CzpVWMW9C/eFcxjSfJZUYtGxrDmld29ngQdMiLKnEmOyiC2zJPsOcCak2eNC0CEsqMebVbbkkxImt6WNajCWVGFJRXcPK7XlMubonPTu08Tsc00pZUokha/ed5MzFSuZNtA5a03IsqcSQ5Vud2d1uGmqju03LsaQSI46dvsjmrFPMmZBKvHXQmhZkSSVGLPnwGAlxYoMHTYuzpBIDLlRUszIzl5mj+9C7k3XQmpZlSSUGrMrMpaSimnsnDfA7FBMDLKm0crW1ypKPjjE2tTPj0rr4HY6JAZZUWrn3DxZy5NRFa6UYz1hSaeV+/8FRenVMZuboPn6HYmKEJZVW7ODJEjYdOsXfXn8VifH2ozbesN+0Vux3G7NpkxjH3ddd5XcoJoZYUmmlTp4v542d+dyVnkrXlCS/wzExxLOk0tgC7SLyiLtO8y4R+bOI2J/XZlj8wRFqapW/u3GQ36GYGONJUglygfZPgHRVvQZYBfzci9hao/PlVfxpSw4zR/chrZstZWq85VVL5dIC7apaCdQt0H6Jqm5Q1VL37RacVQxNEyz96BglFdX8/eTBfodiYpBXSaXRBdrruQ9bAqRJzpdXsWhjNlOG92S0redjfBDyuj8tTUS+DaQDN19m/wJgAUBams0LUt/izUcoLqvih7cN8zsUE6O8aqk0tkA7ACIyFXgcZx3lioYOpKqLVDVdVdN79LB5QQIVl1bx8qYj3D6yF6P6WSvF+MOrpHLFBdoBRGQc8FuchFLoUVytyu82ZVNSUc3DU62VYvzjSVJR1WqgboH2z4AVdQu0i8gst9q/Ae2BlSKyU0Rs0fYQnLlYye8/OMLfXNOHq/t09DscE8M861NpbIF2VZ3qVSyt0Yt/PkRpVQ0PTxnqdygmxtkTta3AgYISlm45xt0T0xjaq4Pf4ZgYZ0klyqkqT7+1l/bJCfxo2pf8DscYSyrR7p09BXyQdZofTRtmY3xMRLCkEsWKy6p4KmMvI/p05G5by8dEiIh7+M0E77l393PqQgUvz59Ags2XYiKE/SZGqY+zT/Onj3P47qSB9ji+iSiWVKJQcVkVj6z4lNSubXlkmj3oZiKLXf5EGVXl8dd3U3C+nJUP3EC7JPsRmshiLZUosyIzl7d2neCR24Yx3pbcMBHIkkoU2Zl7jife2MukId144GabK8VEJksqUaKwpJwHlm6nZ8dkfj1vvC2ybiKWXZBHgcrqWr7/xx2cK6vkv/9hEl3sITcTwSypRLjaWuUnr+9m29GzvDhvHCP62ghkE9ns8ieCOeN69rFqex4PTRnKrDF9/Q7JmEZZUolQqsrP1xzglQ+Pcv9NA3l4qk1pYKKDJZUIpKr84r2D/Ob9w9x9XRo/mXk1ItYxa6KD9alEmNLKan68ahdv7zrBnPRU/mX2KEsoJqpYUokguWdKWbB0OwcKzvPYjOEsmDzIEoqJOpZUIkB1TS2vfHiUX649SEKc8Pt7J3LzMFspwEQnSyo+UlXW7C3g+bWHOHCyhFu/1INn7hhF/y62VKmJXpZUfHCxoprXP8ln6UfHOHCyhEE9UnjpnvHMGNXbLndM1PMsqYjIdOAFIB74T1X9v/X2JwN/AK4FTgNzVPWoV/G1JFUl/1wZ24+d5b19J1n/WSFlVTWM7NuRX941hllj+tokS6bV8CSpiEg8sBC4DWcd5W0ikqGq+wKq3QecVdUhIjIXeA6Y40V84VRVU0tBcTk5Z0rZnV/MJzln2ZFzjqISZ8HFbilJfGN8P74xvj/j0zpby8S0Ol61VCYCWaqaDSAiy4HZQGBSmQ085W6vAn4tIqKq2pwP3pNfzIGCEmpVUQDl0natKqqgOK0JVedrbf0ylJpaqKiuobyqlvKqmkvbZZU1nC2t5PTFSk5fqOBsadVfff6Abu24cUh3xqd1ZlxaF4b37mCtEtOqeZVU+gG5Ae/zgOsuV0dVq0WkGOgGnAqsFOoC7W/uOs5v/5Ld5MDrS4qPIzkxjjaJ8bRJjKNNQjxdUpIY2rM91w/qSreUZPp2bkP/Lu0Y3rsD3donh+2zjYkGUddRq6qLgEUA6enpjbZiHpg8mHsmXkXdVUZcnCBAnAgiIIAEbF8qb6AsOSHephwwphFeJZV8IDXgfX+3rKE6eSKSAHTC6bBtli4pSTZVgDEe8urifhswVEQGikgSMBeovwB7BjDf3f4WsL65/SnGGO950lJx+0geBNbg3FJerKp7ReRpIFNVM4CXgaUikgWcwUk8xpgo41mfiqquBlbXK3syYLscuNOreIwxLcPubRpjwsqSijEmrCSa+0JFpAg45nccAbpT77maKGTnEBki/RyuUtUGh9JHdVKJNCKSqarpfsfRHHYOkSGaz8Euf4wxYWVJxRgTVpZUwmuR3wGEgZ1DZIjac7A+FWNMWFlLxRgTVpZUmkBEnhKRfBHZ6b5mBux7TESyROSAiNweUD7dLcsSkUf9ifzKoiFGABE5KiK73X/7TLesq4isFZFD7tcubrmIyIvuOe0SkfE+xr1YRApFZE9AWchxi8h8t/4hEZnf0Gf5ypmIyF6hvHAmk/rfDZSPAD4FkoGBwGGcsU7x7vYgIMmtM8Lv86gXe8THGBDrUaB7vbKfA4+6248Cz7nbM4F3cGaxuB742Me4JwPjgT1NjRvoCmS7X7u42138/pkEvqylEl6zgeWqWqGqR4AsnFnvLs18p6qVQN3Md5EkGmK8ktnAEnd7CXBHQPkf1LEF6CwiffwIUFU34gyWDRRq3LcDa1X1jKqeBdYC01s++uBZUmm6B91m6eK6JisNz3DX7wrlkSQaYqyjwHsist2dCRCgl6qecLcLgF7udqSfV6hxR/r5RN/Mb14RkXVA7wZ2PQ78BngG55f7GeAXwHe9iy7m3aiq+SLSE1grIvsDd6qqikjU3daM1rjrs6RyGao6NZh6IvI74C337ZVmuGts5ju/BTM7X0RQ1Xz3a6GIvI5z6XZSRPqo6gn3MqHQrR7p5xVq3PnALfXK3/cgzqDZ5U8T1Lsm/zpQ15ufAcwVkWQRGQgMBbYS3Mx3fouGGBGRFBHpULcNTMP59w+cOXA+8D/udgbwv9y7KdcDxQGXG5Eg1LjXANNEpIt72T3NLYscfvcUR+MLWArsBnbh/PD7BOx7HOcuygFgRkD5TOCgu+9xv8/hMucVDTEOwrkz9Smwty5OnJUX/gwcAtYBXd1ywVlz6rD7M0v3MfZlwAmgCqcv5L6mxI1zqZ3lvu71+2dS/2VP1Bpjwsouf4wxYWVJxRgTVpZUjDFhZUnFGBNWllSMMWFlScUYE1aWVEyjRKTGnWZgj4isFJF2IX7/Mnec1A9F5GkRmeqWP3ylY4nIKhEZdIX974tIuru9LmAMlvGRJRUTjDJVHauqo4BK4IHAnSJy2eEeItIbmKCq16jq86r6pKquc3c/DDSYVERkJBCvqtlBxrgU+F6QdU0LsqRiQrUJGCIit4jIJhHJAPaJSBsR+b07edInInKrW/89oJ/b0rlJRF4RkW+JyA+AvsAGEdnQwOfcg/vIuojMks8nxDogIkcC6tW1ZDKAeS1yxiYkllRM0NwWyQycx8bBmXDoIVUdBnwfZ6DtaJz/3EtEpA0wCzjstnQ21R1LVV8EjgO3quqtfNEkYLtbN8P9/rE4j+f/u1unLe5YK3XmFkkWkW5hPm0TIksqJhhtRWQnkAnkAC+75VvVmYwK4EbgvwBUdT/OypHDmvGZfYCiwAIR+SecS7GFIvIQTivlWVWtcKsU4rR+jI9s6gMTjDK3lXCJiABcbMnPBNoEfN5U4E6cKRlR1RdE5Ov1vqeN+33GR9ZSMeGyCacfBBEZBqThjNS+khKgw2X2fQYMcY93Fc6I3TtVtcGkIU6W640zf63xkSUVEy4vAXEisht4FfhOwGXJ5SwC3r1MR+3bfD4Z0Xdwpgh4w+2sXd1A/WuBLapa3ZTgTfjY1AcmIolIW2ADMElVa4Ko/wKQoap/bvHgzBVZS8VEJPcy52cEP6nzHksokcFaKsaYsLKWijEmrCypGGPCypKKMSasLKkYY8LKkooxJqz+P7dPkZq7lgkPAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 288x216 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure(figsize=figsize)\n", "plt.plot(bins[1:], np.cumsum(X_unity))\n", "plt.ylabel(r\"$P(X \\leq x)$\")\n", "plt.xlabel(\"Profit (zł)\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Probability of profit\n", "\n", "From the simulated data I can calculate the probability of making money over the next 100 trades:"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"data": {"text/plain": ["56.63"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(np.sum(X > 0) / X.shape[0] * 100, 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Best case scenario\n", "\n", "In the best case scenario I can expect to make:"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"data": {"text/plain": ["443.62"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["alpha = 0.05\n", "np.round(np.quantile(X, 1-alpha), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The maximum profit in the simulation was:"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"data": {"text/plain": ["1305.32"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(np.max(X), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Worst case scenario\n", "In the worst case scenario I can expect to lose:"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"text/plain": ["-306.93"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(np.quantile(X, alpha), 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The minimum profit in the simulation was:"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"data": {"text/plain": ["-773.0"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["np.round(np.min(X))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.2"}}, "nbformat": 4, "nbformat_minor": 2}