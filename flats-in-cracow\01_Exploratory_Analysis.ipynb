{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# flats-in-cracow exploratory data analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from sklearn.impute import KNNImputer\n", "from pylab import rcParams\n", "from pathlib import Path"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Create directory for images \n", "Path(\"img\").mkdir(parents=True, exist_ok=True)\n", "\n", "# Set default figure size\n", "rcParams['figure.figsize'] = (4, 4)\n", "\n", "# Tell pandas how to display floats\n", "pd.options.display.float_format = \"{:,.2f}\".format"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["path = '../flats-data/cleaned_data.csv'"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["data = pd.read_csv(path, lineterminator='\\n')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 4592 entries, 0 to 4591\n", "Data columns (total 17 columns):\n", " #   Column     Non-Null Count  Dtype \n", "---  ------     --------------  ----- \n", " 0   District   4592 non-null   object\n", " 1   Amount     4592 non-null   int64 \n", " 2   Seller     4592 non-null   object\n", " 3   Area       4592 non-null   int64 \n", " 4   Rooms      4592 non-null   int64 \n", " 5   Bathrooms  4592 non-null   int64 \n", " 6   Parking    4592 non-null   object\n", " 7   Garden     4592 non-null   bool  \n", " 8   Balcony    4592 non-null   bool  \n", " 9   Terrace    4592 non-null   bool  \n", " 10  Floor      4592 non-null   bool  \n", " 11  New        4592 non-null   bool  \n", " 12  Estate     4592 non-null   bool  \n", " 13  Townhouse  4592 non-null   bool  \n", " 14  Apartment  4592 non-null   bool  \n", " 15  Land       4592 non-null   bool  \n", " 16  Studio     4592 non-null   bool  \n", "dtypes: bool(10), int64(4), object(3)\n", "memory usage: 296.1+ KB\n"]}], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>District</th>\n", "      <th>Amount</th>\n", "      <th>Seller</th>\n", "      <th>Area</th>\n", "      <th>Rooms</th>\n", "      <th>Bathrooms</th>\n", "      <th>Parking</th>\n", "      <th>Garden</th>\n", "      <th>Balcony</th>\n", "      <th>Terrace</th>\n", "      <th>Floor</th>\n", "      <th>New</th>\n", "      <th>Estate</th>\n", "      <th>Townhouse</th>\n", "      <th>Apartment</th>\n", "      <th>Land</th>\n", "      <th>Studio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>595000</td>\n", "      <td>realtor</td>\n", "      <td>78</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>podgorze</td>\n", "      <td>449000</td>\n", "      <td>realtor</td>\n", "      <td>61</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>nowa huta</td>\n", "      <td>449000</td>\n", "      <td>realtor</td>\n", "      <td>58</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>595000</td>\n", "      <td>realtor</td>\n", "      <td>78</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>430000</td>\n", "      <td>realtor</td>\n", "      <td>48</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>garage</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    District  Amount   Seller  Area  Rooms  Bathrooms     Parking  Garden  \\\n", "0  krowodrza  595000  realtor    78      4          2  no parking   False   \n", "1   podgorze  449000  realtor    61      3          1  no parking   False   \n", "2  nowa huta  449000  realtor    58      3          1  no parking   False   \n", "3  krowodrza  595000  realtor    78      4          2  no parking   False   \n", "4  krowodrza  430000  realtor    48      2          1      garage   False   \n", "\n", "   Balcony  Terrace  Floor    New  Estate  Townhouse  Apartment   Land  Studio  \n", "0     True    False  False  False   False      False      False  False   False  \n", "1     True    False   True  False   False      False      False  False   False  \n", "2     True    False  False   True   False      False      False  False   False  \n", "3     True    False  False  False   False      False      False  False   False  \n", "4     True    False   True  False    True      False      False  False   False  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Numeric features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To visually inspect the data we are going to make histograms for each of the numeric columns."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["numeric = list(data.select_dtypes('number').columns)    \n", "for col in numeric:\n", "    if col != 'Amount':\n", "        plt.scatter(data[col], data['Amount'], s=2)\n", "        plt.xlabel(f'{col}')\n", "        plt.ylabel(f'Amount (PLN)')\n", "        plt.tight_layout()\n", "        plt.show()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Amount</th>\n", "      <th>Area</th>\n", "      <th>Rooms</th>\n", "      <th>Bathrooms</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Amount</th>\n", "      <td>1.00</td>\n", "      <td>0.71</td>\n", "      <td>0.46</td>\n", "      <td>0.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Area</th>\n", "      <td>0.71</td>\n", "      <td>1.00</td>\n", "      <td>0.74</td>\n", "      <td>0.47</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Rooms</th>\n", "      <td>0.46</td>\n", "      <td>0.74</td>\n", "      <td>1.00</td>\n", "      <td>0.36</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bathrooms</th>\n", "      <td>0.43</td>\n", "      <td>0.47</td>\n", "      <td>0.36</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           Amount  Area  Rooms  Bathrooms\n", "Amount       1.00  0.71   0.46       0.43\n", "Area         0.71  1.00   0.74       0.47\n", "Rooms        0.46  0.74   1.00       0.36\n", "Bathrooms    0.43  0.47   0.36       1.00"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data.select_dtypes('number').corr()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Binary features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We are going to group the data and compare averages."]}, {"cell_type": "code", "execution_count": 9, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["binary = list(data.select_dtypes(bool).columns)    \n", "for col in binary:\n", "    group = data[[col, 'Amount']]\n", "    group = group.groupby([col], as_index=False)\n", "    group = group.mean()\n", "    group = group.sort_values('Amount', ascending=False)\n", "    plt.bar(group[col], height=group['Amount'])\n", "    plt.xticks(ticks=group[col].to_list(),\n", "               labels=group[col].to_list())\n", "    plt.ylabel('Average price (PLN)')\n", "    plt.xlabel(f'{col}')\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We check correlation of binary columns with `Amount`."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Column</th>\n", "      <th>Correlation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Apartment</td>\n", "      <td>0.33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Townhouse</td>\n", "      <td>0.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Terrace</td>\n", "      <td>0.19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Garden</td>\n", "      <td>0.06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Land</td>\n", "      <td>0.06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Balcony</td>\n", "      <td>0.04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Floor</td>\n", "      <td>-0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Estate</td>\n", "      <td>-0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Studio</td>\n", "      <td>-0.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>New</td>\n", "      <td>-0.13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Column  Correlation\n", "0  Apartment         0.33\n", "1  Townhouse         0.20\n", "2    Terrace         0.19\n", "3     Garden         0.06\n", "4       Land         0.06\n", "5    Balcony         0.04\n", "6      Floor        -0.00\n", "7     Estate        -0.00\n", "8     Studio        -0.11\n", "9        New        -0.13"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["C = data[binary].corrwith(data['Amount'])\n", "C.name = 'Correlation'\n", "C = C.to_frame()\n", "C = C.sort_values('Correlation', ascending=False)\n", "C = C.reset_index()\n", "C = C.rename(columns={'index': 'Column'})\n", "C"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Categorical features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Again, we group the rows and compare averages."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["categorical = list(data.select_dtypes('object').columns)    \n", "for col in categorical:\n", "    group = data[[col, 'Amount']]\n", "    group = group.groupby([col], as_index=False)\n", "    group = group.mean()\n", "    group = group.sort_values('Amount', ascending=False)\n", "\n", "    plt.bar(group[col], group['Amount'])\n", "    plt.ylabel('Average price (PLN)')\n", "    plt.xlabel(f'{col}')\n", "    plt.xticks(rotation=90)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(f'img/feature_{col.lower()}.png')\n", "    plt.show()    "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.2"}}, "nbformat": 4, "nbformat_minor": 4}