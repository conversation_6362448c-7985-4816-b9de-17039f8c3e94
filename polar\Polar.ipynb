{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from functools import reduce\n", "from os import listdir\n", "from os.path import isfile, join\n", "from pathlib import Path\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import statsmodels.api as sm\n", "import statsmodels.formula.api as smf\n", "import tabulate\n", "from pylab import rcParams\n", "from scipy.stats import shapiro\n", "from statsmodels.graphics.gofplots import qqplot\n", "from statsmodels.stats.diagnostic import het_goldfeldquandt\n", "from statsmodels.stats.outliers_influence import variance_inflation_factor"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from IPython.display import display, HTML"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Setup"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Set figure size\n", "rcParams['figure.figsize'] = (4, 4)\n", "\n", "# Folder for images\n", "Path('img').mkdir(parents=True, exist_ok=True)\n", "\n", "# Nice float format\n", "pd.options.display.float_format = \"{:,.2f}\".format"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data description\n", "\n", "Last year I purchased a Polar watch that tracks my vitals during workouts. I used the [Polar Flow](polar.flow.com) website to obtain a copy of my data. For privacy reasons I shall not be sharing the dataset."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["path = './data/'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First, we create a list of files in the download."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": true}, "outputs": [], "source": ["files = [f for f in listdir(path) if isfile(join(path, f))]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We shall only consider files containing the string `'training-session'`."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["files = [f for f in files if 'training-session' in f]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The number of files under consideration is:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["284"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(files)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We loop over each of the files and them to a list."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["data = []\n", "\n", "for f in files:\n", "    with open(join(path, f)) as f:\n", "        d = json.load(f)\n", "        data.append(d)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We define a function to extract statistics about heart rate measured during the workouts."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["quantiles = [0.01, 0.25, 0.5, 0.75, 0.99]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def extract_hr_info(workout, quantiles):\n", "    \n", "    stats = {'heartRateAvg2': np.nan,\n", "             'heartRateStd': np.nan}    \n", "\n", "    for q in quantiles:\n", "        stats[f'heartRateQ' + str(int(q * 100))] = np.nan\n", "    \n", "    # Check if data exists\n", "    try:\n", "        heart_rates = workout['exercises'][0]['samples']['heartRate']    \n", "    except KeyError:\n", "        return stats\n", "     \n", "    # Loop over measurements\n", "    hr_data = []\n", "    for hr in heart_rates:\n", "        \n", "        # Check if actually measured hr\n", "        if 'value' in hr:\n", "            hr_data.append(hr['value'])    \n", "\n", "    stats['heartRateAvg2'] = np.mean(hr_data)\n", "    stats['heartRateStd'] = np.std(hr_data)\n", "    \n", "    for q in quantiles:\n", "        stats[f'heartRateQ' + str(int(q * 100))] = np.quantile(hr_data, q)\n", "    \n", "    return stats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We extract the relevant information from the items in the list."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["workouts = []\n", "\n", "for d in data:\n", "    basic = d['exercises'][0]\n", "    hr = extract_hr_info(workout=d, \n", "                         quantiles=quantiles)\n", "    \n", "    workouts.append({**basic, **hr})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally we create a dataframe containing the workout information."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(workouts)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data structure\n", "\n", "We find the following columns in the dataframe."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 284 entries, 0 to 283\n", "Data columns (total 24 columns):\n", " #   Column          Non-Null Count  Dtype  \n", "---  ------          --------------  -----  \n", " 0   startTime       284 non-null    object \n", " 1   stopTime        284 non-null    object \n", " 2   timezoneOffset  284 non-null    int64  \n", " 3   duration        284 non-null    object \n", " 4   sport           284 non-null    object \n", " 5   kiloCalories    283 non-null    float64\n", " 6   heartRate       283 non-null    object \n", " 7   zones           284 non-null    object \n", " 8   samples         284 non-null    object \n", " 9   heartRateAvg2   283 non-null    float64\n", " 10  heartRateStd    283 non-null    float64\n", " 11  heartRateQ1     283 non-null    float64\n", " 12  heartRateQ25    283 non-null    float64\n", " 13  heartRateQ50    283 non-null    float64\n", " 14  heartRateQ75    283 non-null    float64\n", " 15  heartRateQ99    283 non-null    float64\n", " 16  distance        130 non-null    float64\n", " 17  latitude        130 non-null    float64\n", " 18  longitude       130 non-null    float64\n", " 19  ascent          120 non-null    float64\n", " 20  descent         121 non-null    float64\n", " 21  speed           130 non-null    object \n", " 22  autoLaps        102 non-null    object \n", " 23  laps            2 non-null      object \n", "dtypes: float64(13), int64(1), object(10)\n", "memory usage: 53.4+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We remove columns that containt data from features I do not use in my training."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Due to privacy concerns I shan't be extracting longitudinal and latitudinal data."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["df = df.drop(['zones', 'samples', 'autoLaps', \n", "              'laps', 'latitude', 'longitude', \n", "              'ascent', 'descent'], axis=1)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>startTime</th>\n", "      <th>stopTime</th>\n", "      <th>timezoneOffset</th>\n", "      <th>duration</th>\n", "      <th>sport</th>\n", "      <th>kiloCalories</th>\n", "      <th>heartRate</th>\n", "      <th>heartRateAvg2</th>\n", "      <th>heartRateStd</th>\n", "      <th>heartRateQ1</th>\n", "      <th>heartRateQ25</th>\n", "      <th>heartRateQ50</th>\n", "      <th>heartRateQ75</th>\n", "      <th>heartRateQ99</th>\n", "      <th>distance</th>\n", "      <th>speed</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2019-05-24T13:18:14.000</td>\n", "      <td>2019-05-24T14:58:44.125</td>\n", "      <td>120</td>\n", "      <td>PT6030.125S</td>\n", "      <td>STRENGTH_TRAINING</td>\n", "      <td>658.00</td>\n", "      <td>{'min': 72, 'avg': 105, 'max': 136}</td>\n", "      <td>104.77</td>\n", "      <td>11.28</td>\n", "      <td>77.00</td>\n", "      <td>99.00</td>\n", "      <td>105.00</td>\n", "      <td>111.00</td>\n", "      <td>132.00</td>\n", "      <td>nan</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2019-05-04T12:03:34.000</td>\n", "      <td>2019-05-04T13:21:38.500</td>\n", "      <td>120</td>\n", "      <td>PT4684.500S</td>\n", "      <td>STRENGTH_TRAINING</td>\n", "      <td>373.00</td>\n", "      <td>{'min': 71, 'avg': 99, 'max': 138}</td>\n", "      <td>98.65</td>\n", "      <td>12.51</td>\n", "      <td>74.00</td>\n", "      <td>91.00</td>\n", "      <td>97.00</td>\n", "      <td>106.00</td>\n", "      <td>126.00</td>\n", "      <td>nan</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2019-04-12T12:48:57.000</td>\n", "      <td>2019-04-12T12:59:10.750</td>\n", "      <td>120</td>\n", "      <td>PT613.750S</td>\n", "      <td>TREADMILL_RUNNING</td>\n", "      <td>62.00</td>\n", "      <td>{'min': 71, 'avg': 97, 'max': 107}</td>\n", "      <td>97.07</td>\n", "      <td>8.00</td>\n", "      <td>72.00</td>\n", "      <td>94.00</td>\n", "      <td>97.00</td>\n", "      <td>104.00</td>\n", "      <td>107.00</td>\n", "      <td>nan</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2019-06-12T13:13:09.000</td>\n", "      <td>2019-06-12T13:23:15.500</td>\n", "      <td>120</td>\n", "      <td>PT606.500S</td>\n", "      <td>TREADMILL_RUNNING</td>\n", "      <td>71.00</td>\n", "      <td>{'min': 67, 'avg': 105, 'max': 121}</td>\n", "      <td>105.24</td>\n", "      <td>11.25</td>\n", "      <td>67.96</td>\n", "      <td>98.00</td>\n", "      <td>104.00</td>\n", "      <td>118.00</td>\n", "      <td>121.00</td>\n", "      <td>nan</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2019-05-24T14:59:06.000</td>\n", "      <td>2019-05-24T15:29:08.750</td>\n", "      <td>120</td>\n", "      <td>PT1802.750S</td>\n", "      <td>TREADMILL_RUNNING</td>\n", "      <td>416.00</td>\n", "      <td>{'min': 84, 'avg': 144, 'max': 170}</td>\n", "      <td>143.85</td>\n", "      <td>18.47</td>\n", "      <td>87.00</td>\n", "      <td>133.00</td>\n", "      <td>146.00</td>\n", "      <td>158.00</td>\n", "      <td>169.00</td>\n", "      <td>nan</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 startTime                 stopTime  timezoneOffset  \\\n", "0  2019-05-24T13:18:14.000  2019-05-24T14:58:44.125             120   \n", "1  2019-05-04T12:03:34.000  2019-05-04T13:21:38.500             120   \n", "2  2019-04-12T12:48:57.000  2019-04-12T12:59:10.750             120   \n", "3  2019-06-12T13:13:09.000  2019-06-12T13:23:15.500             120   \n", "4  2019-05-24T14:59:06.000  2019-05-24T15:29:08.750             120   \n", "\n", "      duration              sport  kiloCalories  \\\n", "0  PT6030.125S  STRENGTH_TRAINING        658.00   \n", "1  PT4684.500S  STRENGTH_TRAINING        373.00   \n", "2   PT613.750S  TREADMILL_RUNNING         62.00   \n", "3   PT606.500S  TREADMILL_RUNNING         71.00   \n", "4  PT1802.750S  TREADMILL_RUNNING        416.00   \n", "\n", "                             heartRate  heartRateAvg2  heartRateStd  \\\n", "0  {'min': 72, 'avg': 105, 'max': 136}         104.77         11.28   \n", "1   {'min': 71, 'avg': 99, 'max': 138}          98.65         12.51   \n", "2   {'min': 71, 'avg': 97, 'max': 107}          97.07          8.00   \n", "3  {'min': 67, 'avg': 105, 'max': 121}         105.24         11.25   \n", "4  {'min': 84, 'avg': 144, 'max': 170}         143.85         18.47   \n", "\n", "   heartRateQ1  heartRateQ25  heartRateQ50  heartRateQ75  heartRateQ99  \\\n", "0        77.00         99.00        105.00        111.00        132.00   \n", "1        74.00         91.00         97.00        106.00        126.00   \n", "2        72.00         94.00         97.00        104.00        107.00   \n", "3        67.96         98.00        104.00        118.00        121.00   \n", "4        87.00        133.00        146.00        158.00        169.00   \n", "\n", "   distance speed  \n", "0       nan   NaN  \n", "1       nan   NaN  \n", "2       nan   NaN  \n", "3       nan   NaN  \n", "4       nan   NaN  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Missing Values"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The watch tracks different information for different workouts. For example when walking it tracks location but when walking on a treadmill it doesn't, hence there is quite a lot of missing data."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Feature</th>\n", "      <th>Per<PERSON> missing</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>distance</td>\n", "      <td>54.23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>speed</td>\n", "      <td>54.23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>kiloCalories</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>heartRate</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>heartRateAvg2</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>heartRateStd</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>heartRateQ1</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>heartRateQ25</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>heartRateQ50</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>heartRateQ75</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>heartRateQ99</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Feature  Percent missing\n", "0        distance            54.23\n", "1           speed            54.23\n", "2    kiloCalories             0.35\n", "3       heartRate             0.35\n", "4   heartRateAvg2             0.35\n", "5    heartRateStd             0.35\n", "6     heartRateQ1             0.35\n", "7    heartRateQ25             0.35\n", "8    heartRateQ50             0.35\n", "9    heartRateQ75             0.35\n", "10   heartRateQ99             0.35"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["missing = (df.isna().sum() / df.shape[0] * 100)\n", "missing.name = 'Percent missing'\n", "missing = missing.to_frame()\n", "missing = missing.sort_values('Percent missing', ascending=False)\n", "missing = missing[missing['Percent missing'] > 0]\n", "missing = missing.reset_index()\n", "missing = missing.rename(columns={'index': 'Feature'})\n", "np.round(missing, 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Transforms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We apply certain transforms to make the data easier to work with. First we convert strings to datetimes."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["df['startTime'] = pd.to_datetime(df['startTime'])\n", "df['stopTime'] = pd.to_datetime(df['stopTime'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We calculate the total duration of each individual workout in minutes."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"scrolled": false}, "outputs": [], "source": ["df['totalTime'] = (df['stopTime'] - df['startTime'])\n", "df['totalTime'] = df['totalTime'].apply(lambda x: round(x.seconds / 60, 2))\n", "df.drop('duration', axis=1, inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We extract maximum, average and minimum heart rate values from the `heartRate` column."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["df['heartRateMax'] = df['heartRate'].apply(lambda x: x['max'] if isinstance(x, dict) else np.nan)\n", "df['heartRateAvg'] = df['heartRate'].apply(lambda x: x['avg'] if isinstance(x, dict) else np.nan)\n", "df['heartRateMin'] = df['heartRate'].apply(lambda x: x['min'] if isinstance(x, dict) else np.nan)\n", "df.drop('heartRate', axis=1, inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We assume that if there is no `distance` then the workout was indoors:"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["df['isInside'] = df['distance'].apply(lambda x: True if pd.isnull(x) else False)\n", "df = df.drop(['distance', 'speed'], axis=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We are going to map sports to different `activityType`'s. We will map strength training to `1` and cardiovascular work to `0`."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def sport_to_activity_type(x):\n", "    if 'strength' in x.lower():\n", "        return True\n", "    else:\n", "        return False"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["df['isStrength'] = df['sport'].apply(sport_to_activity_type)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["df['sport'] = df['sport'].apply(lambda x: x.lower())\n", "df['sport'] = pd.Categorical(df['sport'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We extract a list of unique `sport` values:"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["sports = sorted(list(df['sport'].unique()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We reorder the alphabetically"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["order = sorted(df.columns.to_list())"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["df = df[order]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We check if there are any more `NaN`'s in the data."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["heartRateAvg      1\n", "heartRateAvg2     1\n", "heartRateMax      1\n", "heartRateMin      1\n", "heartRateQ1       1\n", "heartRateQ25      1\n", "heartRateQ50      1\n", "heartRateQ75      1\n", "heartRateQ99      1\n", "heartRateStd      1\n", "isInside          0\n", "isStrength        0\n", "kiloCalories      1\n", "sport             0\n", "startTime         0\n", "stopTime          0\n", "timezoneOffset    0\n", "totalTime         0\n", "dtype: int64"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isna().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There is one row with `NaN`'s. This might due to my watch having little battery left to make the measurements."]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["df = df.dropna()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We proceed to sort the data with the latest workouts at the top of the dataframe."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["sort_cols = ['startTime','startTime']\n", "df = df.sort_values(sort_cols, ascending=False)\n", "df = df.reset_index(drop=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We verify that the datatypes are correct."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 283 entries, 0 to 282\n", "Data columns (total 18 columns):\n", " #   Column          Non-Null Count  Dtype         \n", "---  ------          --------------  -----         \n", " 0   heartRateAvg    283 non-null    float64       \n", " 1   heartRateAvg2   283 non-null    float64       \n", " 2   heartRateMax    283 non-null    float64       \n", " 3   heartRateMin    283 non-null    float64       \n", " 4   heartRateQ1     283 non-null    float64       \n", " 5   heartRateQ25    283 non-null    float64       \n", " 6   heartRateQ50    283 non-null    float64       \n", " 7   heartRateQ75    283 non-null    float64       \n", " 8   heartRateQ99    283 non-null    float64       \n", " 9   heartRateStd    283 non-null    float64       \n", " 10  isInside        283 non-null    bool          \n", " 11  isStrength      283 non-null    bool          \n", " 12  kiloCalories    283 non-null    float64       \n", " 13  sport           283 non-null    category      \n", " 14  startTime       283 non-null    datetime64[ns]\n", " 15  stopTime        283 non-null    datetime64[ns]\n", " 16  timezoneOffset  283 non-null    int64         \n", " 17  totalTime       283 non-null    float64       \n", "dtypes: bool(2), category(1), datetime64[ns](2), float64(12), int64(1)\n", "memory usage: 34.3 KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>heartRateAvg</th>\n", "      <th>heartRateAvg2</th>\n", "      <th>heartRateMax</th>\n", "      <th>heartRateMin</th>\n", "      <th>heartRateQ1</th>\n", "      <th>heartRateQ25</th>\n", "      <th>heartRateQ50</th>\n", "      <th>heartRateQ75</th>\n", "      <th>heartRateQ99</th>\n", "      <th>heartRateStd</th>\n", "      <th>isInside</th>\n", "      <th>isStrength</th>\n", "      <th>kiloCalories</th>\n", "      <th>sport</th>\n", "      <th>startTime</th>\n", "      <th>stopTime</th>\n", "      <th>timezoneOffset</th>\n", "      <th>totalTime</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>102.00</td>\n", "      <td>102.03</td>\n", "      <td>116.00</td>\n", "      <td>69.00</td>\n", "      <td>73.00</td>\n", "      <td>96.00</td>\n", "      <td>103.00</td>\n", "      <td>109.00</td>\n", "      <td>115.00</td>\n", "      <td>9.02</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>245.00</td>\n", "      <td>walking</td>\n", "      <td>2020-03-29 21:50:21</td>\n", "      <td>2020-03-29 22:23:41.750</td>\n", "      <td>120</td>\n", "      <td>33.33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>104.00</td>\n", "      <td>104.14</td>\n", "      <td>132.00</td>\n", "      <td>70.00</td>\n", "      <td>73.81</td>\n", "      <td>86.00</td>\n", "      <td>110.50</td>\n", "      <td>118.00</td>\n", "      <td>131.00</td>\n", "      <td>16.67</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>401.00</td>\n", "      <td>walking</td>\n", "      <td>2020-03-27 20:38:32</td>\n", "      <td>2020-03-27 21:25:03.750</td>\n", "      <td>60</td>\n", "      <td>46.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>103.00</td>\n", "      <td>103.10</td>\n", "      <td>122.00</td>\n", "      <td>87.00</td>\n", "      <td>91.00</td>\n", "      <td>96.00</td>\n", "      <td>101.00</td>\n", "      <td>110.00</td>\n", "      <td>120.00</td>\n", "      <td>7.94</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>336.00</td>\n", "      <td>walking</td>\n", "      <td>2020-03-26 21:07:46</td>\n", "      <td>2020-03-26 21:52:55.625</td>\n", "      <td>60</td>\n", "      <td>45.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>108.00</td>\n", "      <td>107.97</td>\n", "      <td>125.00</td>\n", "      <td>87.00</td>\n", "      <td>91.00</td>\n", "      <td>103.00</td>\n", "      <td>108.00</td>\n", "      <td>114.00</td>\n", "      <td>124.00</td>\n", "      <td>7.46</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>380.00</td>\n", "      <td>walking</td>\n", "      <td>2020-03-25 19:22:38</td>\n", "      <td>2020-03-25 20:10:17.875</td>\n", "      <td>60</td>\n", "      <td>47.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>117.00</td>\n", "      <td>117.08</td>\n", "      <td>141.00</td>\n", "      <td>90.00</td>\n", "      <td>92.00</td>\n", "      <td>103.00</td>\n", "      <td>120.00</td>\n", "      <td>128.00</td>\n", "      <td>141.00</td>\n", "      <td>13.76</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>358.00</td>\n", "      <td>walking</td>\n", "      <td>2020-03-24 13:09:06</td>\n", "      <td>2020-03-24 13:48:46.750</td>\n", "      <td>60</td>\n", "      <td>39.67</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   heartRateAvg  heartRateAvg2  heartRateMax  heartRateMin  heartRateQ1  \\\n", "0        102.00         102.03        116.00         69.00        73.00   \n", "1        104.00         104.14        132.00         70.00        73.81   \n", "2        103.00         103.10        122.00         87.00        91.00   \n", "3        108.00         107.97        125.00         87.00        91.00   \n", "4        117.00         117.08        141.00         90.00        92.00   \n", "\n", "   heartRateQ25  heartRateQ50  heartRateQ75  heartRateQ99  heartRateStd  \\\n", "0         96.00        103.00        109.00        115.00          9.02   \n", "1         86.00        110.50        118.00        131.00         16.67   \n", "2         96.00        101.00        110.00        120.00          7.94   \n", "3        103.00        108.00        114.00        124.00          7.46   \n", "4        103.00        120.00        128.00        141.00         13.76   \n", "\n", "   isInside  isStrength  kiloCalories    sport           startTime  \\\n", "0     False       False        245.00  walking 2020-03-29 21:50:21   \n", "1     False       False        401.00  walking 2020-03-27 20:38:32   \n", "2     False       False        336.00  walking 2020-03-26 21:07:46   \n", "3     False       False        380.00  walking 2020-03-25 19:22:38   \n", "4     False       False        358.00  walking 2020-03-24 13:09:06   \n", "\n", "                 stopTime  timezoneOffset  totalTime  \n", "0 2020-03-29 22:23:41.750             120      33.33  \n", "1 2020-03-27 21:25:03.750              60      46.52  \n", "2 2020-03-26 21:52:55.625              60      45.15  \n", "3 2020-03-25 20:10:17.875              60      47.65  \n", "4 2020-03-24 13:48:46.750              60      39.67  "]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Given that we have produced a clean dataset we can proceed to analyse a few aspects."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Time span"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The date of the first workout is:"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2019-02-20 20:46:35'"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["str(df['startTime'].min())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The date of the last workout is:"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2020-03-29 21:50:21'"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["str(df['startTime'].max())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Workouts measured:"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["283"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Descriptive statistics"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>heartRateAvg</th>\n", "      <th>heartRateAvg2</th>\n", "      <th>heartRateMax</th>\n", "      <th>heartRateMin</th>\n", "      <th>heartRateQ1</th>\n", "      <th>heartRateQ25</th>\n", "      <th>heartRateQ50</th>\n", "      <th>heartRateQ75</th>\n", "      <th>heartRateQ99</th>\n", "      <th>heartRateStd</th>\n", "      <th>kiloCalories</th>\n", "      <th>totalTime</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>283.00</td>\n", "      <td>283.00</td>\n", "      <td>283.00</td>\n", "      <td>283.00</td>\n", "      <td>283.00</td>\n", "      <td>283.00</td>\n", "      <td>283.00</td>\n", "      <td>283.00</td>\n", "      <td>283.00</td>\n", "      <td>283.00</td>\n", "      <td>283.00</td>\n", "      <td>283.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>105.19</td>\n", "      <td>105.24</td>\n", "      <td>128.34</td>\n", "      <td>76.74</td>\n", "      <td>80.61</td>\n", "      <td>98.24</td>\n", "      <td>105.55</td>\n", "      <td>112.64</td>\n", "      <td>125.84</td>\n", "      <td>10.52</td>\n", "      <td>315.98</td>\n", "      <td>42.83</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>11.87</td>\n", "      <td>11.86</td>\n", "      <td>18.25</td>\n", "      <td>8.99</td>\n", "      <td>8.40</td>\n", "      <td>10.69</td>\n", "      <td>12.26</td>\n", "      <td>14.36</td>\n", "      <td>18.06</td>\n", "      <td>4.46</td>\n", "      <td>218.75</td>\n", "      <td>29.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>82.00</td>\n", "      <td>81.98</td>\n", "      <td>93.00</td>\n", "      <td>53.00</td>\n", "      <td>54.00</td>\n", "      <td>77.00</td>\n", "      <td>82.00</td>\n", "      <td>87.00</td>\n", "      <td>92.00</td>\n", "      <td>2.96</td>\n", "      <td>29.00</td>\n", "      <td>5.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>96.00</td>\n", "      <td>96.42</td>\n", "      <td>115.00</td>\n", "      <td>70.00</td>\n", "      <td>75.00</td>\n", "      <td>91.00</td>\n", "      <td>97.00</td>\n", "      <td>102.00</td>\n", "      <td>113.00</td>\n", "      <td>7.58</td>\n", "      <td>121.50</td>\n", "      <td>15.92</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>103.00</td>\n", "      <td>103.42</td>\n", "      <td>125.00</td>\n", "      <td>77.00</td>\n", "      <td>80.00</td>\n", "      <td>97.00</td>\n", "      <td>104.00</td>\n", "      <td>111.00</td>\n", "      <td>123.00</td>\n", "      <td>10.00</td>\n", "      <td>277.00</td>\n", "      <td>36.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>111.00</td>\n", "      <td>111.26</td>\n", "      <td>138.50</td>\n", "      <td>83.00</td>\n", "      <td>86.00</td>\n", "      <td>103.00</td>\n", "      <td>112.00</td>\n", "      <td>119.00</td>\n", "      <td>135.00</td>\n", "      <td>12.25</td>\n", "      <td>441.50</td>\n", "      <td>65.29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>148.00</td>\n", "      <td>148.35</td>\n", "      <td>178.00</td>\n", "      <td>99.00</td>\n", "      <td>107.00</td>\n", "      <td>146.00</td>\n", "      <td>151.00</td>\n", "      <td>160.00</td>\n", "      <td>177.00</td>\n", "      <td>27.10</td>\n", "      <td>1,067.00</td>\n", "      <td>172.73</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       heartRateAvg  heartRateAvg2  heartRateMax  heartRateMin  heartRateQ1  \\\n", "count        283.00         283.00        283.00        283.00       283.00   \n", "mean         105.19         105.24        128.34         76.74        80.61   \n", "std           11.87          11.86         18.25          8.99         8.40   \n", "min           82.00          81.98         93.00         53.00        54.00   \n", "25%           96.00          96.42        115.00         70.00        75.00   \n", "50%          103.00         103.42        125.00         77.00        80.00   \n", "75%          111.00         111.26        138.50         83.00        86.00   \n", "max          148.00         148.35        178.00         99.00       107.00   \n", "\n", "       heartRateQ25  heartRateQ50  heartRateQ75  heartRateQ99  heartRateStd  \\\n", "count        283.00        283.00        283.00        283.00        283.00   \n", "mean          98.24        105.55        112.64        125.84         10.52   \n", "std           10.69         12.26         14.36         18.06          4.46   \n", "min           77.00         82.00         87.00         92.00          2.96   \n", "25%           91.00         97.00        102.00        113.00          7.58   \n", "50%           97.00        104.00        111.00        123.00         10.00   \n", "75%          103.00        112.00        119.00        135.00         12.25   \n", "max          146.00        151.00        160.00        177.00         27.10   \n", "\n", "       kiloCalories  totalTime  \n", "count        283.00     283.00  \n", "mean         315.98      42.83  \n", "std          218.75      29.65  \n", "min           29.00       5.00  \n", "25%          121.50      15.92  \n", "50%          277.00      36.45  \n", "75%          441.50      65.29  \n", "max        1,067.00     172.73  "]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["df.drop('timezoneOffset', axis=1).describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Kilocalories burned in total"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First we count the total `kiloCalories` I burned during the period in question."]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["89421.0\n"]}], "source": ["total_calories = df['kiloCalories'].sum()\n", "print(total_calories)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We convert this number to kilograms of body fat.\n", "According to [this article](https://www.livestrong.com/article/304137-how-many-calories-per-kilogram-of-weight/) it equates to"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["def kcal_to_kg(x):\n", "    return round(x / 7700, 2)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["11.61"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["kcal_to_kg(total_calories)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Kilocalories burned by sport"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sport</th>\n", "      <th>Total kilocalories</th>\n", "      <th>Total kilograms</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>walking</td>\n", "      <td>33080</td>\n", "      <td>4.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>strength_training</td>\n", "      <td>31547</td>\n", "      <td>4.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>treadmill_running</td>\n", "      <td>19825</td>\n", "      <td>2.57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>cycling</td>\n", "      <td>4029</td>\n", "      <td>0.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>running</td>\n", "      <td>940</td>\n", "      <td>0.12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               Sport  Total kilocalories  Total kilograms\n", "4            walking               33080             4.30\n", "2  strength_training               31547             4.10\n", "3  treadmill_running               19825             2.57\n", "0            cycling                4029             0.52\n", "1            running                 940             0.12"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["by_sport = df[['kiloCalories', 'sport']].groupby('sport', as_index=False)\n", "by_sport = by_sport.sum()\n", "by_sport['sport'] = by_sport['sport'].apply(lambda x: x.lower())\n", "by_sport['kiloCalories'] = by_sport['kiloCalories'].astype(int)\n", "by_sport = by_sport.rename(columns={'kiloCalories': 'Total kilocalories', 'sport': 'Sport'})\n", "by_sport = by_sport.sort_values('Total kilocalories', ascending=False)\n", "by_sport['Total kilograms'] = by_sport['Total kilocalories'].apply(kcal_to_kg)\n", "\n", "# by_sport = by_sport.style.background_gradient(cmap='YlGn', subset='Total kilograms')\n", "# by_sport = by_sport.set_precision(2)\n", "\n", "by_sport"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Kilocalories burned over time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next we produce a plot of `kiloCalories` burned over a two month period in 2019. First we extract the relevant data."]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["start = pd.to_datetime('2019-04-1') \n", "stop = pd.to_datetime('2019-06-1')\n", "\n", "daily = df[['startTime', 'kiloCalories']]\n", "mask = (daily['startTime'] >= start) & (daily['startTime'] < stop)\n", "daily = daily[mask]\n", "daily['startTime'] = daily['startTime'].dt.date\n", "daily = daily.groupby('startTime', as_index=False)\n", "daily = daily.sum()\n", "daily = daily.sort_values('startTime', ascending=False)\n", "daily['startTime'] = pd.to_datetime(daily['startTime'])\n", "daily = daily.reset_index(drop=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We create a dataframe with all the dates to perform a left join and fill the `NaN`'s with zeroes."]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["dates = pd.date_range(start, stop)\n", "dates = dates.to_frame()\n", "dates = dates.reset_index(drop=True)\n", "dates.columns = ['startTime']"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["daily = pd.merge(dates, daily, on='startTime', how='left')\n", "daily = daily.fillna(0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally we produce the figure:"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["width = 800\n", "height = 400\n", "dpi = 100\n", "\n", "plt.figure(figsize=(width/dpi, height/dpi))\n", "plt.plot(daily['startTime'], daily['kiloCalories'])\n", "\n", "plt.fill_between(x=daily['startTime'], \n", "                 y1=0, \n", "                 y2=daily['kiloCalories'], \n", "                 alpha=1/2)\n", "\n", "daily_avg = daily['kiloCalories'].mean()\n", "\n", "plt.hlines(xmin=daily['startTime'].min(),\n", "           xmax=daily['startTime'].max(),\n", "           y=daily_avg,\n", "           linestyle='dashed',\n", "           label=f'Daily average = {round(daily_avg)} kcal',\n", "           alpha=1/2)\n", "\n", "plt.title('Kilocalories burned over time', fontsize=18)\n", "plt.xticks(rotation=45, horizontalalignment='center')\n", "plt.xlim(daily['startTime'].min(), daily['startTime'].max())\n", "plt.ylim(0, daily['kiloCalories'].max() * 1.05)\n", "plt.ylabel('Kilocalories')\n", "plt.legend(loc='best')\n", "plt.tight_layout()\n", "plt.savefig('./img/kilocalories_ts.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Kilocalories by intensity"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.scatter(df['heartRateQ1'], df['heartRateQ99'], c=df['kiloCalories'])\n", "plt.xlabel('0.01 quantile of heart rate (bpm)')\n", "plt.ylabel('0.99 quantile of heart rate (bpm)')\n", "\n", "cbar = plt.colorbar()\n", "cbar.set_label('Kilocalories', rotation=270)\n", "plt.savefig('./img/intensity_scatter.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Workouts by sport"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We check how many workouts I completed."]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sport</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>walking</td>\n", "      <td>105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>treadmill_running</td>\n", "      <td>90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>strength_training</td>\n", "      <td>62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>cycling</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>running</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               Sport  Count\n", "4            walking    105\n", "3  treadmill_running     90\n", "2  strength_training     62\n", "0            cycling     24\n", "1            running      2"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["stats = df[['sport', 'startTime']]\n", "stats = stats.groupby(['sport'], as_index=False)\n", "stats = stats.count()\n", "stats = stats.rename(columns={'sport': 'Sport', \n", "                              'startTime': 'Count'})\n", "stats = stats.sort_values('Count', ascending=False)\n", "\n", "# stats = stats.style.background_gradient(cmap='YlGn', subset='Count')\n", "# stats = stats.set_precision(2)\n", "\n", "stats"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## By hour of day"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We count workouts by hour of day."]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["by_hour = df[['startTime', 'sport']].copy()\n", "by_hour['startHour'] = by_hour['startTime'].dt.hour\n", "by_hour = by_hour.drop('startTime', axis=1)\n", "by_hour = by_hour.groupby('startHour', as_index=False)\n", "by_hour = by_hour.count()\n", "\n", "all_hours = pd.DataFrame(range(0, 24), columns=['startHour'])\n", "\n", "by_hour = pd.merge(all_hours, by_hour, how='left')\n", "by_hour = by_hour.fillna(0)\n", "by_hour = by_hour.sort_values('startHour')\n", "by_hour = by_hour.rename(columns={'startHour': 'Hour of day', \n", "                                 'sport': 'Total workouts'})"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.bar(by_hour['Hour of day'], by_hour['Total workouts'])\n", "plt.ylabel('Number of workouts')\n", "plt.xlabel('Hour of day')\n", "plt.tight_layout()\n", "plt.savefig('./img/workouts_by_hour_of_day.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## By day of week"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We count workouts by day of week."]}, {"cell_type": "code", "execution_count": 48, "metadata": {"scrolled": false}, "outputs": [], "source": ["by_day = df[['startTime', 'sport']].copy()\n", "by_day['Day of week'] = pd.to_datetime(by_day['startTime']).dt.day_name()\n", "by_day['Day number'] = pd.to_datetime(by_day['startTime']).dt.dayofweek\n", "by_day = by_day.groupby(['Day of week', 'Day number'], as_index=False)\n", "by_day = by_day.count()\n", "by_day = by_day.drop('startTime', axis=1)\n", "by_day = by_day.sort_values('Day number')\n", "by_day = by_day.rename(columns={'sport': 'Total Workouts'})"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAQ8AAAEoCAYAAABGhnSwAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAcqUlEQVR4nO3debhcVZnv8e8vYZQpDAGZQpgEsWUyMgsCzRUNkwjYCNyIadPaCii2wvWKgNiKCtjo5bamQQzKIPMotDTN4ISQhFmgwZDQzCAgIQqY8PYfa1VSOZyc7Oxk7apz6vd5nnqq9q5T9a6dqry19pq2IgIzs0U1rNMFMLPBycnDzGpx8jCzWpw8zKwWJw8zq2WpThegijXWWCNGjx7d6WKY9ZwpU6a8EBEj+3tuUCSP0aNHM3ny5E4Xw6znSJqxoOd82mJmtTh5mFktTh5mVouTh5nV4uRhZrU4eZhZLU4eZlaLk4eZ1eLkYWa1DIoRpmajj7+u6PtPP3Vs0fcfilzzMLNanDzMrBYnDzOrxcnDzGpx8jCzWpw8zKwWJw8zq8XJw8xqcfIws1qcPMysFicPM6vFycPManHyMLNanDzMrJaiU/IlTQdmAnOA2RExRtJqwM+A0cB04JCIeKlkOcxsyWui5rF7RGwdEWPy9vHATRGxKXBT3jazQaYTpy37A5Py40nAAR0og5ktptLJI4BfSJoiaULet1ZEPJ0fPwOsVbgMZlZA6WUId4mIJyWtCdwo6aH2JyMiJEV/L8zJZgLAqFGjChfTzBZV0ZpHRDyZ758DrgC2A56VtDZAvn9uAa+dGBFjImLMyJEjSxbTzGooljwkrSBppdZj4H8B9wNXA+Pyn40DripVBjMrp+Rpy1rAFZJacS6IiBsk3QlcLGk8MAM4pGAZzKyQYskjIqYBW/Wz/4/AnqXimlkzPMLUzGpx8jCzWpw8zKwWJw8zq8XJw8xqcfIws1pKD0+3gnzleOsk1zzMrBYnDzOrxcnDzGpx8jCzWpw8zKwWJw8zq2VIddW669KsOa55mFktTh5mVouTh5nV4uRhZrU4eZhZLU4eZlaLk4eZ1eLkYWa1OHmYWS1OHmZWi5OHmdWy0OQhaWNJy+bH75d0tKQR5YtmZt2sSs3jMmCOpE2AicD6wAVFS2VmXa9K8ngzImYDHwa+HxFfBNYuWywz63ZVksdfJR0KjAOuzfuWLlckMxsMqqzncSTwKeCfI+IxSRsCP6kaQNJwYDLwZETsk19/EbA6MAU4IiLeWPSimw1dg2Ftmio1j70i4uiIuBAgIh4DXluEGMcAD7Ztfwv4bkRsArwEjF+E9zKzLlEleYzrZ9/Hq7y5pPWAscDZeVvAHsCl+U8mAQdUeS8z6y4LPG3J7RwfAzaUdHXbUysBL1Z8/38BvpRfA+lU5eXcAAvwBLDuAuJPACYAjBo1qmI4K20wVKetGQO1efwGeBpYAzi9bf9M4N6FvbGkfYDnImKKpPcvasEiYiKpa5gxY8bEor7ezMpaYPKIiBnADGDHmu+9M7CfpA8BywErA2cCIyQtlWsf6wFP1nx/M+ugKiNMZ0p6Jd9ekzRH0isLe11E/J+IWC8iRgN/B/xnRBwG3AwclP9sHHDVYpTfzDpkoV21EdFqr2g1eO4P7LAYMY8DLpL0deAu4JzFeC8z65BFmhgXyZXABxbxdbdExD758bSI2C4iNomIgyPi9UV5LzPrDguteUg6sG1zGDCGRRvnYWZDUJURpvu2PZ4NTCeduphZD6vS5nFkEwUxs8GlSm/LepKukPRcvl2WR46aWQ+r0mB6LnA1sE6+XZP3mVkPq5I8RkbEuRExO99+DIwsXC4z63JVkscfJR0uaXi+HQ78sXTBzKy7VUkenwAOAZ4hzXU5iIqzas1s6KqSPLaIiP0iYmRErBkRB5Cm2ZtZD6uSPE6QtEdrQ9IX8TgPs55XZZDYfsC1OWnsDWyOk4dZz6sySOwFSfsB/0Fac/SgiPD6GmY9bqCVxGYC7UliGWAj4CBJERErly6cmXWvgRYDWilPwV8/Ih5vsExmXcPLLi7YgA2m+fSk7L+emQ1KVXpbpkp6b/GSmNmgUqW3ZXvgMEkzgFmASJWSLYuWzMy6WpXksUirhplZb1joaUteRX0EaVGgfYEReZ+Z9bAq63kcA5wPrJlvP5V0VOmCmVl3q3LaMh7YPiJmAUj6FvBb4PslC2Zm3a1Kb4uAOW3bc/I+M+thVWoe5wK/k3RF3j4AX2vFrOdVmdtyhqRbgF3yriMj4q6ipTKzrlflui2nALcB57TaPczMqrR5TAMOBSZLukPS6ZI8Jd+sx1UZ53FuRHwC2B34KXBwvjezHlbltOVsYAvgWeCXpDVMpxYul5l1uSqnLasDw4GXgReBFyJidtFSmVnXq9Lb8mEASe8kzXO5WdLwiBjwqnGSliM1tC6b41waESdK2hC4iJSUpgBHRMQbi3cYZta0Kqct+wDvA3YlzXH5T9Lpy8K8DuwREa9KWhr4laTrgWOB70bERZJ+QBrB+q91D8DMOqPKILG9ScnizIh4quob54WEXs2bS+dbAHsAH8v7JwEn4eRhNuhUOW35bN03lzScdGqyCXAW8Afg5bY2kyeAdRfw2gnABIBRo0bVLYKZFVKlwbS2iJgTEVsD6wHbkS7bUPW1EyNiTESMGTnSl8Y16zZFk0dLRLwM3AzsCIyQ1KrxrAc82UQZzGzJWmDykHRTvv9WnTeWNFLSiPx4eWAv4EFSEjko/9k44Ko6729mnTVQm8faknYC9pN0EX2m4UfEwgaKrQ1Myu0ew4CLI+JaSb8HLpL0deAuPEPXbFAaKHl8FTiBdGpxRp/nWr0mCxQR9wLb9LN/Gqn9w8wGsYEu+nQpcKmkEyLilAbLZGaDQJWu2lPytWp3zbtuiYhryxbLzLpdlQWQvwkcA/w+346R9I3SBTOz7lZlhOlYYOuIeBNA0iRSQ+eXSxbMzLpb1XEeI9oer1KiIGY2uFSpeXwTuEvSzaTu2l2B44uWysy6XpUG0wvzAsiti10fFxHPFC2VmXW9KjUPIuJp4OrCZTGzQaSRuS1mNvQ4eZhZLQMmD0nDJT3UVGHMbPAYMHlExBzgYUlejcfM5lOlwXRV4AFJdwBzrxgXEfsVK5WZdb0qyeOE4qUws0GnyjiPWyVtAGwaEf8h6W2k67iYWQ+rMjHuk8ClwA/zrnWBK0sWysy6X5Wu2s8AOwOvAETEI8CaJQtlZt2vSvJ4vf2Kbnnx4ihXJDMbDKokj1slfRlYXtJewCXANWWLZWbdrkryOB54HrgP+Afg58BXShbKzLpfld6WN/MCQL8jna48nC8laWY9rMqFrscCPyBdKlLAhpL+ISKuL104M+teVQaJnQ7sHhGPAkjaGLgOcPIw62FV2jxmthJHNg2YWag8ZjZILLDmIenA/HCypJ8DF5PaPA4G7mygbGbWxQY6bdm37fGzwG758fPA8sVKZGaDwkBXjDuyyYKY2eBSpbdlQ+AoYHT733tKvllvq9LbciXpSvbXAG9WfWNJ6wPnAWuR2komRsSZklYDfkZKRtOBQyLipUUrtpl1WpXk8VpEfK/Ge88GvhARUyWtBEyRdCPwceCmiDhV0vGkEazH1Xh/M+ugKsnjTEknAr8AXm/tjIipA70oX67h6fx4pqQHSdP59wfen/9sEnALTh5mg06V5PFu4AhgD+adtkTerkTSaGAb0hD3tXJiAXiGdFpjZoNMleRxMLBR+7T8RSFpReAy4HMR8Yqkuc9FREjqd56MpAnABIBRo7p7/eXRx19X9P2nnzq26Pub1VFlhOn9zH+h68okLU1KHOdHxOV597OS1s7Prw08199rI2JiRIyJiDEjR46sE97MCqpS8xgBPCTpTuZv8xiwq1apinEO8GBEnNH21NXAOODUfH/VohbazDqvSvI4seZ770xqK7lP0t1535dJSeNiSeOBGcAhNd/fzDqo0urpdd44In5FmsLfnz3rvKeZdY8qI0xnMm/N0mWApYFZEbFyyYKZWXerUvNYqfU4t2PsD+xQslBm1v2q9LbMFcmVwAcKlcfMBokqpy0Htm0OA8YArxUrkZkNClV6W9rX9ZhNmsy2f5HSmNmgUaXNw+t6mNlbDLQM4VcHeF1ExCkFymNmg8RANY9Z/exbARgPrA44eZj1sIGWITy99Tivx3EMcCRwEelyDGbWwwZs88irfh0LHEZae2Nbr/plZjBwm8d3gAOBicC7I+LVxkplZl1voEFiXwDWIV3U+ilJr+TbTEmvNFM8M+tWA7V5LNLoUzPrLU4QZlaLk4eZ1eLkYWa1OHmYWS1OHmZWi5OHmdXi5GFmtTh5mFktTh5mVouTh5nV4uRhZrU4eZhZLU4eZlaLk4eZ1eLkYWa1OHmYWS3FkoekH0l6TtL9bftWk3SjpEfy/aql4ptZWSVrHj8G9u6z73jgpojYFLgpb5vZIFQseUTEbcCLfXbvT1qFnXx/QKn4ZlZW020ea0XE0/nxM8BaC/pDSRMkTZY0+fnnn2+mdGZWWccaTCMigBjg+YkRMSYixowcObLBkplZFU0nj2clrQ2Q759rOL6ZLSFNJ4+rgXH58Tjgqobjm9kSUrKr9kLgt8Bmkp6QNB44FdhL0iPA3+ZtMxuEBrxW7eKIiEMX8NSepWKaWXM8wtTManHyMLNanDzMrBYnDzOrxcnDzGpx8jCzWpw8zKwWJw8zq8XJw8xqcfIws1qcPMysFicPM6vFycPManHyMLNanDzMrBYnDzOrxcnDzGpx8jCzWpw8zKwWJw8zq8XJw8xqcfIws1qcPMysFicPM6vFycPManHyMLNanDzMrBYnDzOrxcnDzGrpSPKQtLekhyU9Kun4TpTBzBZP48lD0nDgLOCDwBbAoZK2aLocZrZ4OlHz2A54NCKmRcQbwEXA/h0oh5ktBkVEswGlg4C9I+Lv8/YRwPYR8dk+fzcBmJA3NwMeLlCcNYAXCryv43Y2puMuORtExMj+nliqQLAlIiImAhNLxpA0OSLGlIzR63F76Vh7LW4nTlueBNZv214v7zOzQaQTyeNOYFNJG0paBvg74OoOlMPMFkPjpy0RMVvSZ4F/B4YDP4qIB5ouR1b0tMhxOxbTcRvQeIOpmQ0NHmFqZrU4eZhZLU4eDZD07k6XoSmSVu90GawZPZc88vD4pv1/SXdI+kdJq3QgfpNul3SJpA9JUpOBO/TZdkynj7fnkgfwiKTvNDmfJiLeBxxGGt8yRdIFkvYqHVfSUZJWLR2nj3eQWv6PIP1bf0PSOxqK3fhnCyDpdEnvajJm1pHjbenF5LEV8F/A2ZJulzRB0sqlg0bEI8BXgOOA3YDvSXpI0oEFw64F3Cnp4jyTuXhNIJIbI+JQ4JPAOOAOSbdK2rFw+I58tsCDwERJv5P0qQZrl5063iQievZG+k/8JDALmARsUijOlsB38wd9FrBt3r8OMKPwMQr4AGkC4qPAN4CNC8ZbHTgGmAxcBxxIGk80BnhsqH22fWJuBpwKzAAuAHYfysfbczUPScMl7SfpCuBfgNOBjYBrgJ8XCvt9YCqwVUR8JiKmAkTEU6TaSDGRvlnP5NtsYFXgUknfLhTyt8DKwAERMTYiLo+I2RExGfhBoZhAxz7bubGBzfPtBeAe4FhJF5WM2anjBXqv5gFMA84Bdurnue91unxL+FiPAaaQRvMeDCyd9w8D/lAopnrtsyXVKh8Bfghs1+e5h4fa8bZuPTfCVNKKEfFqwzE3Bb5JWvxoudb+iNiocNyTScP/Z/Tz3Dsj4sECMUcCXwLexfzHuseSjtVP7MY/2xz3SODiiJjVz3OrRMSfCsXtyPHOjd+DyWM5YDxv/XJ/omDMXwEnkn6h9gWOBIZFxFdLxewTf03mP9bHC8b6BfAz4J+AT5EaTJ+PiONKxWyL3fhn2xZ7VWDTPnFvKxyzY8cLvdnb8hPg7aRGxFtJSwLMLBxz+Yi4iZSsZ0TEScDYwjGRtK+kR4DHSMc6Hbi+cNjVI+Ic4K8RcWv+IhevdWSd+GyR9PfAbaTTw5Pz/Uml49Kh452riXPRbroBd+X7e/P90sDthWP+hpSoLwc+C3yYgufCbXHvIfV+tI55d+CcwjFvz/f/TkqQ21CofaUbPtsc5z7SL//deXtz4PKherytWy/WPP6a71+W9DfAKsCahWMeA7wNOBp4D2kA1bjCMSH9+v8RGCZpWETcTOoyLenreZzDF0inLmcDny8cs6UTny3AaxHxGoCkZSPiIVK3bWmdOl6gi5chLGhiPj89gbQI0YpA0baHiLgzP3yV1N7RlJclrUiqUp8v6TnSOIBiIuLa/PBPpJpOkxr/bLMnJI0ArgRulPQSaaxHaZ06XqAHG0ybJOkaYIH/wBGxX+H4KwCvkQaKHUb6ZTo/10aWdKzvM/CxHr2kY3YjSbuR/p1viHR1gCGrZ2oeko4d6PmIOKNA2NPy/YGkhq2f5u1DgWcLxJtPzN91OKlwuMn5fmdSl/TP8vbBwO9LBu7QZ4uk1frZfV++XxF4sVDcjhxvXz2TPICV8v1mwHuZt27qvsAdJQJGxK2QJk7F/CtbXyNp8gJettgkzWTgWsASn/8QEZNy7E8Du0TE7Lz9A+CXSzpeH41/ttkU0r+zgFHAS/nxCOBxYMNCcTt1vPNrqmW2W26k8/+V2rZXAm4rHPNBYKO27Q2BBxs41lOAf8zHuDLwaeBrhWM+DKzWtr0qDfQsdeqzzXH+DfhQ2/YHgR8O1eNt3Xqp5tGyFtB+LvpG3lfS54FbJE0j/TJtwLwLWpW0X0Rs1bb9r5LuoWyj2qnAXZJuJh3rrjQz5gE689kC7BARn2xtRMT1BecOtevU8QK9ddrSch5pivgVefsACrcHRMQNeYj65nnXQxHxesmY2SxJh5Fm1AapraV0b8u5kq4Hts+7jouIZ0rGbNPfZ/vjBuI+JekrzGvTOgx4qoG4jX+X2/Vkb4uk9wC75M3bIuKuwvEOJrW+z8xfsm2Br0eeXVsw7mjgTFIjZgC/Bj4XEdMLxtyZNFhqlqTDScd6ZvQzv6ZQ/G2B9+XN4p9tjrkaafrBrqR/59tIp4dFGkz7xG70uzxf7B5NHsNJ1bu5Na8oO9/j3ojYUtIupHaI04CvRsT2C3npoCPpXtIiNVsC55JmfR4SEbsVjLlyRLyygN4PSv4nzt+l8yLisFIxKsRv7LvcrudGmEo6itRNeiNwLWnBmmsHfNHim5PvxwL/FhHXAcsUjomkb0taWdLSkm6S9HyuDZQ0O9Iv0v7AWRFxFvN6B0q5IN9PIXUZt26t7WIiYg6wgdLVDxvVoe/yvPi9VvOQ9CiwfRQYKDVAzGtJqzztRarG/wW4o09jZom4d0fE1pI+DOwDHEuq2haLK+lW4AbSSNpdgeeAeyKi6AryeYnF9Zv61e0T+zzgnaQu07ltSlF4vEUnvsvteq7mAfw3aeh0kw4hTRT7QES8DKwGfLGBuK2q7Fjgkii0rkQfHwVeB8bnhtL1gO+UDpprO9eVjrMAfyD94g8j1bJat9I68V2eqxd7W6aRuk2vI33JgbK/EhHx5zyvZBfSilOz831p10p6iFTT+XReqOe1UsHy+feFETF3TkuuCZxXKmYfUyW9N+bNJWpERJzcZLw2jX+X2/Vi8ng835ahgXYHAEknkmazbkZqRFya1K23c8m4EXF8Hm/wp4iYI+nPpLaIUvHmSHqz5OpZC7E9cLik6aTTB6VixZYlg+YxLW85/4/yq6c1/l1u13NtHi15tinRwDJuku4mrWsxNSK2yfvubeBL/TZSO8eoiJiQx5psFvNmvpaIeRXpWG9k/vP/YhPjJI2KiMclbdDf86W7iXN3actywEdIDcdfKhm303qu5pHXPfgJqd0BSS8A/zsiHigY9o2ICEmRY65QMFa7c0k9Djvl7SeBSyjbIn95vjXpStLlLGZIuiwiPtJk8IiY0mfXryUVn2PSwRoP0IPJg3Q1s2MjLYyDpPeT5ibsNNCLFtPFkn4IjJD0SeATOWZpG0fERyUdCnPbXope+CnyBLmGtR9T0UWl+w0+//iSYaQFn5q48NM/tT2eW+NpIC7Qm8ljhVbiAIiIW0rXBCLiNKXLS75Cavf4akTcWDJm9oak5cm/TpI2pq1hrQRJj9H/r2HJ/9SxgMdNaZ9dO5u0Zuz40kE7VeNp6cXkMU3SCaRTF4DDSa3WReVk0UTCaHciaczF+pLOJzXQfrxwzPalB5YjrefR78jPJWgrSa+Q/vMunx/DvAbT0pdgfGfkZQhbJC1bOGZ/NZ4xNFPjSfF7rcE0L9t2MvPmA/wSOCkiXioYs319jWVIvS2zGvhSI2l1YAfSf6TbI+KF0jH7KcOUiHjPwv9ycJI0NSK2Xdi+AnHba3mzSavjfy0iflUybkvP1Txykmh0SbyImDtgKLc57E/6D92E5UiL1CwFbCGJKHg9kTwxraX1azgkv2eS3g6sS6rtbMO8tpeVSQtel4r7XuC/I2LDvD2O1N4xncKrts1Xjl6peUi6eqDno8B6opKWiryiVj/P3dXqti1F0rdIIz4fAN7Mu6PEsbbFvLlts/VreFpEPFwqZqfk/7QfJyXI9jk0M4EfR0SRXidJU4G/jYgXJe1KWnLhKGBr0inUQSXivqUcPZQ8nicN570Q+B3zt9ATecnAJRxzakRsK+nAtt2tX+PdImLHJR2zT/yHgS0bWjukZ0n6SERc1mC8e1rzkySdRboi30l5++6I2LqJcgzJ6uQCvJ00Me1Q4GOkeRAXFh7f0bIvbz03LbpyejaN1L7SWPLIDYUfAUYz/zTxrzVVhqZFxGWSxvLWyz6WOubhbbXaPZl/VbrG/k/3TPLIU6dvAG7IX/BDSfMCTo6I/1co7JpKK13f37c4pAs/lZ6D8Gfgbkk3Mf/ch5JtPleRJmtNocGk1UlKizy/jXSdmrOBgyi7EPGFwK15gONfyAtMS9qEBifK9UzygLm/imNJiWM08D3gioFes5iGk5bgLzowawBXM29l7aasFxF7Nxyz03bKiz3dGxEnSzqdgtcEjoh/zj8IawO/iHltD8NIbR+N6Jnkkddc+Bvg58DJEdG3NlDC052srndotOdvJL07Iu5b+J8OGX/J93+WtA7pei1rlwwYEbf3s++/Ssbsq2eSB2kw2CzSdWOPbhulXXIgUUdqHJLuY+DrtizxCXmS7if16CwFHKm0UvzrNDSztcOuVbrc5LdJp2uQTl+GtJ5JHhHRiYWP9uxATEirhgF8Jt+3j6Yt1b22LqmrsGe0jbc4JW+vSLpi3EPAdztZtib0TFdtL+pvLEmpkY9NjKjsNt0y3qJTeqbm0aMkaeeI+HXe2IlyS0+2epb61dTqVg0bHvNWZv8oMDGP97gsr+EypDl5DG3jgR9Jak2Wepm0HEAJne5Z6oSuGG/RKUP+AHtZnrK9VSt5FF4asKM9Sx3SFeMtOsVtHkOYpLWAbwDrRMQHJW0B7BgR5xSIVXyuTjeStAPzxlvMyvveAawYha8I2GlOHkOY0jVjzwX+b0RsJWkp4K4ocA0VSatFA5dXtO7Ri9dt6SVrRMTF5Bm1+dx8zsAvqceJo/c4eQxts/JiQK1lCHegB87FrRk+bRmCJH0O+E3ePIM0LP8BYCRwcETc06my2dDh5DEESTqNtBr85qTRjk8Ct5GWIGh8GUIbmpw8hjClK7ePISWSHfPt5YjYoqMFsyHB4zyGtuVJ62mukm9PkeZemC021zyGIEkTSatazSQtuXg7aeX0YivEW+9xb8vQNApYFniG1N7xBGloutkS45rHEJUv8fAuUnvHTqQelxeB30bEiZ0smw0NTh5DnKT1SFeK24m0zsfqETGis6WyocDJYwiSdDTzahx/JY35aN3ui4g3B3i5WSXubRmaRgOXAJ+PiKc7XBYbolzzMLNa3NtiZrU4eZhZLU4eZlaLk4eZ1fI/FbiYBKxpfiUAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.bar(by_day['Day of week'], by_day['Total Workouts'])\n", "plt.xticks(rotation=90)\n", "plt.ylabel('Number of workouts')\n", "plt.savefig('./img/workouts_by_day_of_week.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scatter plot of walks data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We plot `totalTime` versus `kiloCalories`. As can be seen their seems to exist a linear relationship between the two."]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAARcAAAEGCAYAAABPWdHqAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAd3UlEQVR4nO3de5ScdZ3n8feHJECCgIEEzMgloAEGWAmkuXjQiILcjMKqA96RiTIX1DA4KujBW87syjCCYVFcJA4wCw6IygrLcAkgrHMk0g0tCQkkAQmEjSTBzgUDIYnf/eP5Vaiu9KW6u37VVV2f1zl1uuqpp+r5PZ3km9/9q4jAzKzWdhjuApjZyOTgYmZZOLiYWRYOLmaWhYOLmWUxergLMBQTJkyIyZMnD3cxzFpOR0fHmoiY2Nc5TR1cJk+eTHt7+3AXw6zlSFre3zluFplZFlmDi6RZkhZKekLSBenYHpLulbQ0/RyfjkvSlZKWSXpc0lE5y2ZmeWULLpIOBz4LHAMcAcyQ9FbgIuC+iJgC3JdeA5wGTEmP84Crc5XNzPLLWXP5S2B+RGyMiC3Ag8AHgTOA69M51wNnpudnADdE4WHgjZImZSyfmWWUM7gsBN4paU9J44DTgX2BvSNiZTrnD8De6fmbgefLPr8iHetG0nmS2iW1r169Ol/pzWxIsgWXiFgMXArcA9wFdAJbK84JYEArJyPimohoi4i2iRP7HAkzs2GUtUM3IuZGxLSImA50AUuAF0vNnfRzVTr9BYqaTck+6ZiZNaHco0V7pZ/7UfS33AT8EjgnnXIO8L/T818Cn0qjRscB68qaT2bDpmN5F5+aO5+O5V3DXZSmknsS3c8k7QlsBs6PiLWSvgPcImkmsBw4K517J0W/zDJgI3Bu5rKZVWXOvCU8tHQNADfMPHaYS9M8sgaXiHhnD8deAk7s4XgA5+csj9lgzDrpoG4/rTpNPf3frB6m7T/eNZZB8PR/M8vCwcXMsnBwMbMsHFzMLAsHFzPLwsHFzLJwcDGzLBxczCwLBxczy8LBxcyycHAxsywcXMwsCwcXM8vCwcXMssi9E90/pJxFCyX9RNLOkg6QND/lJ7pZ0o7p3J3S62Xp/ck5y2ZmeeXMW/Rm4AtAW0QcDowCPkKxafcVEfFWin11Z6aPzAS60vEr0nlm1qRyN4tGA2MljQbGASuB9wC3pvcr8xaV8hndCpwoSZnLZ2aZ5Ewt8gLwL8BzFEFlHdABrE1J0qB7bqJteYvS++uAPXOVz8zyytksGk9RGzkA+AtgF+DUGnyvk6KZNYGczaKTgN9HxOqI2Az8HDieIk1rae/e8txE2/IWpfd3B16q/FInRTNrDjmDy3PAcZLGpb6TE4FFwAPAh9M5lXmLSvmMPgzcnzICmFkd1Do/U84+l/kUHbOPAgvSta4BvgJcKGkZRZ/K3PSRucCe6fiFwEW5ymZm2yvlZ5ozb0lNvi933qJvAN+oOPwMcEwP574K/FXO8phZ72qdn8l5i8wMqH1+Jk//N7MsHFzMLAsHFzPLwsHFzLJwcDGzLBxczCwLBxczy8LBxcyycHAxa3K1XhNUKw4uZk2u1muCasXT/82aXK3XBNWKg4tZk6v1mqBacbPIzLJwcDGzLBxczCyLnBt0Hyyps+yxXtIFkvaQdK+kpenn+HS+JF2ZkqI9LumoXGUzs/xybnP5VERMjYipwDRgI/ALiu0r74uIKcB9vL6d5WnAlPQ4D7g6V9nMLL96NYtOBJ6OiOV0T35WmRTthig8TJElYFKdymdmNVav4PIR4Cfp+d4RsTI9/wOwd3q+LSlaUp4wzcyaTPbgkhLNfwD4aeV7KXXIgNKHOCla42vU6ehWX/WouZwGPBoRL6bXL5aaO+nnqnR8W1K0pDxh2jZOitb4GnU6utVXPYLLR3m9SQTdk59VJkX7VBo1Og5YV9Z8siYy66SDmD5lQsNNR7f6Us6khpJ2oci8eGBErEvH9gRuAfYDlgNnRcQfU1bGqyjySW8Ezo2I9r6+v62tLdrb+zzFzDKQ1BERbX2dkzsp2p8osiqWH3uJYvSo8twAzs9ZHjOrH8/QNbMsHFzMLAsHFzPLwsHFzLJwcDGzLBxczCwLBxczy8LBxcyycHAxsywcXMwsCwcXM8vCwcXMsnBwMbMsHFzMLAsHF6srb4HZOhxcrK68BWbryBpcJL1R0q2SnpS0WNLbnRSttXkLzNaRu+YyB7grIg4BjgAW46RoLW3a/uO5YeaxTNt//HAXxTLLmc51d2A6MBcgIl6LiLU4KZpZS8hZczkAWA38q6THJF2bNuweUlI05y3qmztMrVHkDC6jgaOAqyPiSOBPvN4EAgaXFM15i/rmDlNrFDl3/18BrIiI+en1rRTB5UVJkyJi5WCSolnfSh2l7jC14VZVzUXS8alJg6RPSLpc0v59fSYi/gA8L+ngdOhEYBFOipaVO0ytUVRbc7kaOELSEcAXgWuBG4B39fO5zwM3pnzRzwDnUgS0WyTNJCVFS+feCZwOLCMlRRvAfZhZg6k2uGyJiJB0BnBVRMxNwaFPEdEJ9JSVzUnRzEa4aoPLBkkXA58E3ilpB2BMvmKZWbOrdrTobGAT8NepL2Uf4LJspTKzpldVcEkB5WfATunQGuAXuQpl9eW5MZZDtaNFn6UYSv6f6dCbgdtyFcrqy3NjLIdqm0XnA8cD6wEiYimwV65CWe31VTvxYkLLodoO3U0R8ZokACSNZoAza214lWonADfMPLbbe6W5MWa1VG1weVDSV4Gxkt4L/D1we75iWa155q7Vm4rpJf2cVAw9zwROBgTcDVwb1Xw4o7a2tmhvbx/OIpi1JEkdEdHTHLZtqqq5RMSfgR+lh5lZv/rs0JV0S/q5IO0O1+1RnyKOTB7+tZGuv5rLrPRzRu6CtJq+OljNRoI+g0vaFmEUcF1EvLtOZWoJ7mC1ka7fPpeI2Crpz5J2j4h19ShUK/Dwr4101Q5FvwwskHQvxY5yAETEF7KUysyaXrXB5efpYWZWlWqHoq9PGz6VOgieiojN+YplZs2u2oWLJwBLge8DPwCWSJpexeeeTcPYnZLa0zEnRTNrAdUuXPwucHJEvCsipgOnAFdU+dl3R8TUstl8Topm1gKqDS5jIuKp0ouIWMLgd6JzUjSzFlBtcGlPSc1OSI8fAdUs6gngHkkdks5Lx5wUzawFVDta9HcUe7qUhp7/L0XfS3/eEREvSNoLuFfSk+Vvpk2/B5wUDbgGioWLA/msmdVPtdtcboqIyyPig+lxRURsquJzL6Sfqyi2xTyGlBQNoNWSonk9kbWS/hYu9rhgsZqFi5J2kbRr6TnFdg0LaeGkaN5O0lpJf82ioSxY3Bv4Rdq9bjRwU0TcJekRWjQpWj3WE3Us72LOvCXMOukgZ120YVXVZlGNyptFbe9Tc+fz0NI1TJ8ywWuXLJuabRaVmin/A/hLYEdgFPCniNhtyKW0mvJqa2sU1Q5FXwV8lGKW7ljgMxSzdS2jwXQAOxG9NYpqgwsRsQwYFRFbI+JfgVPzFcvAHcDW3Kqd57IxLVzslPTPwEoGEJhscNzEsWZWbYD4ZDr3cxT7uewLfChXoazQTE0cz+GxStXWXNYAr0XEq8C30taXO/XzGWsh3hPYKlVbc7kPGFf2eiwwr/bFsf40ag3BKWGtUrXBZeeIeLn0Ij0f18f5VqWBBotG7eRtpiac1Ue1weVP5Zs3SZoGvJKnSK1loMGiljWERq0F2chQbZ/LBcBPJf0/inSubwLOzlaqFjLQEaFaZg1wP4nlVO0euo9IOgQ4OB3yHro1MpwpRjzUbTlVu4fuGIo9Xb6ZHn+Tjlkd9Nd8GWzzxv0kllO1fS5XA9MoNoj6QXruPW7rpL9+mUbt5LXWVm2fy9ERcUTZ6/sl/S5Hgex1pe0TTj282Eq4t+aLmzfWiKoNLlslvSUingaQdCCwNV+xDGD27U/QuWId61/ZzG2fe0ev5zk1rDWiaptFXwIekPQrSQ8C9wNfzFcsA6DYaOv1n5l5aNpqqdo9dO+jyCf0BeDzwMER8UA1n5U0StJjku5Irw+QND8lP7s5LYhE0k7p9bL0/uTB3NBIcsmMQ5k+ZQJnte1bl3/07ruxWupvD90Plh7A+4C3psf70rFqzAIWl72+FLgiIt4KdAEz0/GZQFc6fkU6r6WVmjt3LVzZ7z/6WtQ6PIXfaqm/Ppf39/Fe0E9yekn7UASlfwIuVLGh7nuAj6VTrqcY2r6aIinaN9PxW4GrJCmaeR/OAehr79tqOmxrMSHOfTdWS30Gl4gY6ibZ3wO+DOyaXu8JrI2ILel1eeKzbUnRImKLpHXp/DXlX5iSq50HsN9++w2xeI2jr+BQzT96jxhZo+kzuEj6RET8L0kX9vR+RFzex2dnAKsioiMlsq+JkZoUbajBwbUOazT9NYt2ST937eG9/v5hHw98QNLpwM7AbsAcihzQo1PtpTzxWSkp2gpJo4HdgZf6v4WRYaDBwSlErNH1F1zuBIiIb1W+kWomvYqIi4GL07knAP8YER+X9FPgw8C/s31StHOA36T372+V/pbB8KJDa3T9DUXf29OQsKRzKWohg/EVis7dZRR9KnPT8bnAnun4hcBFg/z+pjWQER+P7Fij6zMpWmrSfA94X0QsTccuphjtOS0iVtSllL1o9qRolU0bJzSzZlFNUrQ+ay4RcSfFauj/kHS4pO9RDE9PH+7A0owqayaVk9a8EZSNJP3O0E2zc88FfgUcCLwnIvw3dhD6CybVbIFQbdDwbFsbbv0NRW+gGBUSxW7/JwKr0mS4cDrXgakcbh7M8HEpaCx4YR3XnnN0r4HI815suDkRfWYdy7uYffsTIHHJjEOrHjYu748Buj3/zPWP0LVxs/tmbNjULBG9Dd6ceUvoXLEOgNl3LOK284+v+nOloWag27Dztecc3S3YmDUiB5fMZp10EI8918WGTVthALXEnpo1vTWnPKHOGpGbRXUw2KZRtTyEbfU25KFoq41p+49nt7Fj6Hx+bZbRG0+os0bkZlEddCzvYv0rm5m67xuzBAAvWrRG5JpLHZQ6dXfbefR2TSJPdrORysGlDiqbLeUBZbCT3RyUrNG5WVQHlc2W8mHmwU5286poa3QOLsOgPKAMtr/EM3Ct0blZlFG1TZfBNHHK1yG5iWSNyMElo976UyqPV77uWN7Fmd//T8686tdVBQwvUrRG5GZRJh3Lu1j/6ham7rP7dk2XyiZN5c8585bQ+fzabc+9Obc1o2wzdCXtDDxEsZp6NHBrRHxD0gEUW1zuCXQAn4yI1yTtBNxAkeT+JeDsiHi2r2s08gzdocya7Vjexew7FkEEZx29H3ctXOmp/dZQhnuG7iaKvV+OAKYCp0o6jhZJijaUWbPT9h/Pbecfz22fe0dVCdHMGlG24BKFl9PLMekRFEnRbk3HrwfOTM/PSK9J75+Y9o1pStVs/FQNT+23ZpW1Qzflie4EVgH3Ak9TZVI0oJQUrfI7z5PULql99erVOYtfU4Md0alVkDKrt6zBJSK2RsRUivxExwCH1OA7r4mItohomzhx4pDLWC/lIzo5h6jNGkVdRosiYq2kB4C306JJ0cpHdKqdXetZuNbMsgUXSROBzSmwjAXeS9FJ+wAjPClaac3QqYdP6jbSUwoQ1Q4de4jZmlnOoei3UXTQjqJoft0SEd+WdCBFYNkDeAz4RERsSkPX/wYcCfwR+EhEPNPXNRp1KLo0DD1+3Jiq97r1bnLWTIZ1D92IeJwiUFQef4ai/6Xy+KvAX+UqTz2VahqnHj6JWx55jvWvbqFjede2qfo9BZHZdyyi8/m1rH91S9X77Jo1Mk//r8JAO1ZLTaCPHbvfdjvQ9TpVv1SDbM6WoNl2PP2/CkPpWO1rqn95LeaS9x/mHf1tRPEG3VUYaH9Ited7Y21rVs5bVCMD2XOlY3nXtqRl0HdNx6NBNpI5uNTYnHlL6Nq4mfHjxvQbNLyxto1k7tCtgfIO39JaoGvPORrAM2ytZTm41ECpw/cz1z8CsG0t0GCm/JuNFA4uQ9SxvIuV615llKBr4+ZuQ8zlK5q9W5y1Gve5DNGceUtYuqrYWWLcmB1of7aL917+IN/50NsGNeXfbKRwzWWQSs2cUw+fxNR938jUfXZn1A47sHHzVpauerlbDcVT+60VueYySOUT60rT9W+a/xz//c5FvGn3sd1qKF7dbK3INZdBKu9PKdViDn7Trlx8+qGseXkTT/1hQ4/nmrUKz9CtQm9bKJSUz7Rd8MK6bfNcHvv6ydnLZjYchnuD7qZVOWxcatZcdveTPY74zDrpIKbuszvrX93C2W37Mn7cGL50yiEefraW5j6XHlT2kZSaM3vssiN3PL6SQyfttu3cUq0Gic7n17LbzqO31VhKNZrS95i1EtdcelDZR1IaUn5wyWq2/Dm4uf35beduC0QR2/WruK/FWlnOnej2pUhytjdFSpFrImKOpD2Am4HJwLPAWRHRldKIzAFOBzYCn46IR/u6Rr13ortp/nNcdveTfOmUQ/jYsfsBHma21lRNn0vO4DIJmBQRj0ralSK74pnAp4E/RsR3JF0EjI+Ir0g6Hfg8RXA5FpgTEX22JRp1m8vBcqCyZjGsHboRsbJU84iIDcBiitxE5cnPKpOi3ZCSqT1MkSVgUq7y1UKtO2y9RMBGkrr0uUiaTLGf7nxg74hYmd76A0WzCcqSoiXlCdPKv6thkqJVBoOhBhv30dhIkn20SNIbgJ8BF0TE+vIMrRERkgbULouIa4BroGgW1bKsA1W5XmioM3G9v4uNJFmDi6QxFIHlxoj4eTr8oqRJEbEyNXtWpeOlpGgl5QnTGlJlMPDiRLPXZWsWpdGfucDiiLi87K1S8jPYPinap1Q4DlhX1nxqKL01f5zX2ex1OWsuxwOfBBakZPQAXwW+A9wiaSawHDgrvXcnxUjRMoqh6HMzlm1IvBDRrH85k6L9GlAvb5/Yw/kBnJ+rPLXk5o9Z/zxDdwBKzSHAzR+zfji4DMDs25/goaVrmH37E8NdFLOG5+AyEKVhdPXW2jOzEgeXAbhkxqFMnzKBS2YcOtxFMWt43nJhADzJzax6rrmYWRYOLmaWhZtFScfyrmIUSOKSGYd6mNlsiFqu5tLb1P0585bQuWIdnc+vZfYdi4apdGYjR8sFl972TJl10kGMGzMKgD9t2jIcRTMbUVquWdTb1P1p+4/noL3fQOeKdeyy46jhKJrZiNJywaWv4eRL3n/Ytm0mzWxoWq5ZBMVG20d++x5umv9ct+PeMsGsdloyuFx295N0bdzMZXc/OdxFMRuxWjK4fOmUQ7ZlRTSzPFquz6VjeRd3LVzJtecc7eaPWUbZgoukHwMzgFURcXg6VrOEaAPVsbyL2Xcs4ulVG9iwaSvgXeTMcsrZLLoOOLXi2EXAfRExBbgvvQY4DZiSHucBV9e6MHPmLaHz+bVs2LSV8ePGeETILLOcSdEeAv5YcXjYEqLNOukgpuz1BnbdaRRfOuUQN4nMMqt3h+6QEqLB4JKildKk7rLjKDZs2spdCxsyqYDZiDJso0VpQ+4BJzWLiGsioi0i2iZOnNjv+R3Lu/jM9Y8Uu/VLzmhoVif1Hi2qe0K02bc/QdfGzey60yivdjaro3rXXOqfEC3td/uWvXZ1YDGro5wZF38C/AY4WNKKlATtO8B7JS0FTkqvoUiI9gxFQrQfAX9fq3J431uz4aGi66M5tbW1RXt7+3AXw6zlSOqIiLa+zmnJ6f9mlp+Di5ll4eBiZlk4uJhZFg4uZpaFg4uZZeHgYmZZNPU8F0mrgeUZLzEBWJPx+319X79Zr79/RPS5uK+pg0tuktr7myjk6/v6vn7P3CwysywcXMwsCweXvl3j6/v6vv7guM/FzLJwzcXMsnBwMbMsHFwSSftKekDSIklPSJqVjn9T0guSOtPj9IxleFbSgnSd9nRsD0n3SlqafmbZTk/SwWX32ClpvaQLct6/pB9LWiVpYdmxHu837VJ4paRlkh6XdFSm618m6cl0jV9IemM6PlnSK2W/hx9mun6vv29JF6f7f0rSKZmuf3PZtZ+V1JmOD/z+I8KPot9pEnBUer4rsAQ4FPgm8I91KsOzwISKY/8MXJSeXwRcWodyjKLIzrB/zvsHpgNHAQv7u1+KhHn/AQg4Dpif6fonA6PT80vLrj+5/LyM99/j7zv9XfwdsBNwAPA0MKrW1694/7vA1wd7/665JBGxMlKWx4jYACyml/QmddZbrqecTgSejoics5+JYc5t1dP1I+KeiNiSXj5MsVl8Fr3cf2/OAP49IjZFxO8ptoQ9Jtf1UxbUs4CfDPb7HVx6IGkycCQwPx36XKom/zhXsyQJ4B5JHZLOS8d6y/WU00fo/peqXvcPNchtVUN/TVFbKjlA0mOSHpT0zozX7en3Xe/7fyfwYkQsLTs2oPt3cKkg6Q3Az4ALImI9RWrZtwBTgZUUVcVc3hERR1Gktz1f0vTyN6Oon2adOyBpR+ADwE/ToXrefzf1uN/eSPoasAW4MR1aCewXEUcCFwI3Sdotw6WH7fdd4aN0/w9mwPfv4FJG0hiKwHJjRPwcICJejIitEfFniswEQ6qK9iUiXkg/VwG/SNd6sVT9V/dcT7mcBjwaES+mstTt/pPe7jdbbqtKkj4NzAA+ngIcqTnyUnreQdHnUfPsen38vut5/6OBDwI3l5VrwPfv4JKkNuZcYHFEXF52vLxd/1+BhZWfrdH1d5G0a+k5RcfiQnrP9ZRLt/+x6nX/Zeqf26qMpFOBLwMfiIiNZccnShqVnh8ITKFIh1Pr6/f2+/4l8BFJO0k6IF3/t7W+fnIS8GRErCgr18Dvv5a93838AN5BUQV/HOhMj9OBfwMWpOO/BCZluv6BFKMBvwOeAL6Wju8J3AcsBeYBe2T8HewCvATsXnYs2/1TBLGVwGaKPoSZvd0vxSjR9yn+x1wAtGW6/jKKvo3S34EfpnM/lP5cOoFHgfdnun6vv2/ga+n+nwJOy3H9dPw64G8rzh3w/Xv6v5ll4WaRmWXh4GJmWTi4mFkWDi5mloWDi5ll4eDS5CRtTatUn5D0O0lflFSzP1dJn5b0F2Wvr5V0aI2++0xJXx/gZ+4srVQexPWmapCruiXtKOmhNMHMquDg0vxeiYipEXEY8F6KGbbfGMgXlCZH9eLTwLbgEhGfiYhFgyloD74M/GAgH4iI0yNi7SCvN5Vi7tKARcRrFPNvzh7ktVuOg8sIEsWygfMoFr4p1TquKr0v6Q5JJ6TnL0v6rqTfAW+X9HVJj0haKOma9PkPA23Ajal2NFbSryS1pe/4qIr9ZxZKurTsOi9L+qdUk3pY0naLLSUdBGyKiDXp9XWSrk7nPyPphLRwb7Gk68o+96ykCSr2F1ks6Uep1naPpLHpnPIyTkif2RH4NnB2upez06zoH0v6bVqQd0b6zGHpWKeKBYRT0uVvAz5emz+tkc/BZYSJiGco9mPZq59Td6HYE+WIiPg1cFVEHB0RhwNjgRkRcSvQTrHGZmpEvFL6cGoqXQq8h6JGcLSkM8u+++GIOAJ4CPhsD9c/nmKmZ7nxwNuBf6CYnXoFcBjwXyRN7eE7pgDfT7W2tRSzSHuUah5fB25O93IzxYzX+yPiGODdwGVp6cXfAnMiYipFcC1Ng18IHN3bNaw7B5fWtZVikWbJuyXNl7SAImAc1s/njwZ+FRGro9j/5EaKzYcAXgPuSM87KDYaqjQJWF1x7PYopowvoFjuvyCKBXxP9PIdv4+Izn6u05eTgYtU7Lb2K2BnYD/gN8BXJX2FIrPgKwARsRV4rbQGzPrmzqkRJi0q20qxmngL3f8D2bns+avpHwuSdqbo+2iLiOclfbPi3IHaHK+vK9lKz3/PXgF2rzi2Kf38c9nz0uuevqP8nK0UNS7oft993YeAD0XEUxXHF0uaD7wPuFPS30TE/em9nYBX+/hOS1xzGUEkTQR+SNHECYptM6dK2kHSvvS+XULpH+AaFfvZfLjsvQ0U235W+i3wrtSnMYpiNfWDAyjuYuCtAzh/IJ4FpqXnfd3L3cDnJQlA0pHp54HAMxFxJcWq7Lel43sCayJic6ZyjygOLs1vbGkommIV8T3At9J7/wn8HlgEXMn2fRwApNGXH1H0KdwNPFL29nXAD0sdumWfWUmxx+0DFCu5OyJiINtBPAQcWfqHXWP/AvydpMcokqmXPAAcWurQBWYDY4DH0+9vdjrvLGBhai4dDtyQjr8b+D8ZyjsieVW0DRtJcyj6WeYNd1mqIennFJuHLxnusjQD11xsOP03YNxwF6IaaSj7NgeW6rnmYmZZuOZiZlk4uJhZFg4uZpaFg4uZZeHgYmZZ/H9JjvZj0f+J2QAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["walking = df[df['sport'] == 'walking']\n", "plt.scatter(walking['totalTime'], walking['kiloCalories'], s=2)\n", "plt.xlabel('Duration (minutes)')\n", "plt.ylabel('Kilocalories')\n", "plt.savefig('./img/walks_kilocalories_vs_time.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We plot `heartRateAvg` against `kiloCalories`. Again we see a linear relationship although there are a couple of outliers"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAARUAAAEGCAYAAABLrAHXAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAcHElEQVR4nO3de7hcdX3v8ffHBIGkCCGJNCIhKEGs9HBJCvikpGiQm2A4FuRiRTCVnnOwQmlR0KNU7UVKK8ZDxabmlODhFm5VOQiBeEF9TMoORBJJSCIKwgkQMAQhEiB8zx/rN2Ey3XvPzJ61ZtbMfF7PM8/MrFkz89s7Wd/9u38VEZiZ5eV1nS6AmfUWBxUzy5WDipnlykHFzHLloGJmuRrd6QK0YsKECTFlypROF8Os7yxbtuzpiJg42GtdHVSmTJnCwMBAp4th1nckPTLUa27+mFmuCg0qks6TtFLSzySdn47tLukuSWvT/bh0XJK+ImmdpAckHVJk2cysGIUFFUkHAB8FDgUOBE6QtC9wEbA4IqYCi9NzgOOAqel2DnBlUWUzs+IUWVN5O7A0IjZHxCvAD4D3A7OBBemcBcBJ6fFs4OrILAF2kzSpwPKZWQGKDCorgSMkjZc0Bjge2AvYIyLWp3OeAPZIj/cEflX1/sfSse1IOkfSgKSBDRs2FFd6MxuRwoJKRKwCLgUWAXcAy4GtNecE0NSKxoiYFxHTI2L6xImDjmiZWQcV2lEbEfMjYlpEzAQ2AmuAJyvNmnT/VDr9cbKaTMWb0zEz6yJFj/68Md1PJutPuRb4FvDhdMqHgW+mx98CzkyjQIcDm6qaSX1h2SMbOXP+UpY9srHTRTEbsaInv90saTzwMnBuRDwr6YvAQklzgEeAD6Rzbyfrd1kHbAbOLrhspTP37jXcs/ZpAK6ec1iHS2M2MoUGlYg4YpBjzwCzBjkewLlFlqfszjtqv+3uzbpRV0/T7zXT9h7nGop1PU/TN7NcOaiYWa4cVMwsVw4qZpYrBxUzy5WDipnlykHFzHLloGJmuXJQMbNcOaiYWa4cVMwsVw4qZpYrBxUzy5WDipnlquid3/4i5fxZKek6STtJ2kfS0pTf5wZJr0/n7pier0uvTymybGZWjCLz/uwJfByYHhEHAKOA08g2w748IvYl27d2TnrLHGBjOn55Os/MukzRzZ/RwM6SRgNjgPXAu4Gb0uu1eX8q+YBuAmZJUsHlM7OcFZmi43HgH4FHyYLJJmAZ8GxKLgbb5/bZlvcnvb4JGF9U+cysGEU2f8aR1T72Ad4EjAWOzeFznUzMrMSKbP4cBfwiIjZExMvALcAMsnSmlb1xq3P7bMv7k17fFXim9kOdTMys3IoMKo8Ch0sak/pGZgEPAt8DTk7n1Ob9qeQDOhn4btphv2c5z4/1oiL7VJaSdbjeB6xI3zUP+CRwgaR1ZH0m89Nb5gPj0/ELgIuKKltZVPL8zL17TaeLYpabovP+XAJcUnP4YeDQQc59ETilyPKUjfP8WC9y3p8Ocp4f60Wepm9muXJQMbNcOaiYWa4cVMwsVw4qZpYrBxUzy5WDipnlykHFzHLV80HF62vM2qvng4rX15i1V89P0/f6GrP26vmg4vU1Zu3V880fM2svBxUzy5WDipnlqsiNr98maXnV7TlJ50vaXdJdktam+3HpfEn6Skom9oCkQ4oqm5kVp8jtJB+KiIMi4iBgGrAZuJVsm8jFETEVWMxr20YeB0xNt3OAK4sqm5kVp13Nn1nAzyPiEbZPGlabTOzqyCwh23V/UpvKZ2Y5aVdQOQ24Lj3eIyLWp8dPAHukx9uSiSXVicbMrEsUHlRSAvb3ATfWvpZScDSVhsPJxHqfl1Z0t3bUVI4D7ouIJ9PzJyvNmnT/VDq+LZlYUp1obBsnE+t9XlrR3doRVE7ntaYPbJ80rDaZ2JlpFOhwYFNVM8n6yHlH7cfMqRO8tKJLqcgkgJLGkmUqfEtEbErHxgMLgcnAI8AHIuLXKYvhFWT5ljcDZ0fEwHCfP3369BgYGPYUMyuApGURMX2w14pOJvYCWRbC6mPPkI0G1Z4bwLlFlsfMiucZtWaWKwcVM8uVg4qZ5cpBxcxy5aBiZrlyUDGzXDmomFmuHFTMLFcOKmaWKwcVM8uVg4qZ5cpBxcxy5aBiZrlyUDGzXDmotJG3SbR+4KDSRt4m0fpBoUFF0m6SbpK0WtIqSe/s52Ri3ibR+kHRNZW5wB0RsT9wILCKPk4mNm3vcVw95zCm7T2u00UxK0yRaU93BWYC8wEi4qWIeBYnEzPraUXWVPYBNgD/Jul+SV9PG2G3lEzMeX+s09zhPrwig8po4BDgyog4GHiB15o6wMiSiTnvj3WaO9yHV+Ru+o8Bj0XE0vT8JrKg8qSkSRGxfiTJxMw6rdLR7g73wTVUU5E0IzVdkPQnkr4kae/h3hMRTwC/kvS2dGgW8CBOJmZdzh3uw2u0pnIlcKCkA4G/BL4OXA38UZ33/TlwTcqn/DBwNlkgWyhpDimZWDr3duB4YB0pmVgTP4eZlUSjQeWViAhJs4ErImJ+CgrDiojlwGBZzJxMzKxHNRpUfiPpYuBDwBGSXgfsUFyxzKxbNTr6cyqwBfhI6it5M3BZYaUys67VUFBJgeRmYMd06Gng1qIKZb3Hczv6R6OjPx8lGxL+l3RoT+DfiyqU9R7P7egfjTZ/zgVmAM8BRMRa4I1FFcp6TyuLKV3L6S6NdtRuiYiXJAEgaTRNzoS1/laZ2zESlVoOMOLPsPZpNKj8QNKngJ0lvQf4H8C3iyuW2Ws8g7W7KJseUuekbAh5DnA0IOBO4OvRyJsLNH369BgYGOhkEcz6kqRlETHYHLTGaioR8Srwr+lmZjakYTtqJS1M9yvSbmzb3dpTRDNrh7w6xOvVVM5L9ye09C1mVnp5dYgPG1TS9gSjgKsi4l0j/hYzK728OsTr9qlExFZJr0raNSI2tfRtZlZarQz7V2t0SPl5YIWku8h2cAMgIj7ecgnMrKc0GlRuSTczs2E1OqS8IG20VGlsPRQRLxdXLDPrVo0uKDwSWAv8M/BVYI2kmQ2875dpOHq5pIF0rG+TiZn1g0YXFP4TcHRE/FFEzASOAS5v8L3vioiDqmbf9W0yMbN+0GhQ2SEiHqo8iYg1jHznNycTM+thjQaVgZQM7Mh0+1egkUU3ASyStEzSOemYk4mZ9bBGR3/+O9meKpUh5B+S9a3U84cR8bikNwJ3SVpd/WLaTLvpZGLAPMgWFDbzXjMrXqPbSW6JiC9FxPvT7fKI2NLA+x5P90+RbT95KCmZGICTiZm1powbWNVbUDjoQsJGFhRKGitpl8pjsm0TVuJkYma5KeM2nfWaP60sJNwDuDXtFjcauDYi7pB0L04mZk1Y9shG5t69hvOO2s9ZAWuUcQOrhjZpKitv0tQfzpy/lHvWPs3MqRO8nWRJtLxJU2qO/C/g7cDrgVHACxHxhtxKaTaEMv41tqE1OqR8BXA62azanYE/JZtda1a4VhOil7Ezs5c1GlSIiHXAqIjYGhH/BhxbXLHM8lPGzsxe1ug8lc1pQeFySf8ArKeJgGTWSW4+tVejgeFD6dyPke2nshfwx0UVqpNcVe49rTafrDmN1lSeBl6KiBeBz6UtJnes856u5MRVZq1ptKayGBhT9Xxn4O78i9N5raTntMa5Rti7Gg0qO0XE85Un6fGYYc7vWq4qt0eZOk8d4PLVaFB5oXrTJEnTgN8WUyQbSi/95y9TjbBMAa4XNNqncj5wo6T/R5b29HeBUwsrlQ2ql/p78tq5PQ8eHcpXo3vU3itpf+Bt6ZD3qO0A/+cvRpkCXC9odI/aHcj2VPnrdPuzdMxqFNlE6Zf+nl5q5vWjRvtUrgSmkW3M9NX02HvIDsLt89b5d9jdGu1T+YOIOLDq+Xcl/bSIAnU7N1FGpnp7A/8Ou1ujQWWrpLdGxM8BJL0F2FpcsbpXmdvnZd6XpLYTuqy/Q6uv0aByIfA9SQ+Tjf7sTZ9solTmC7FZZR49cu2kdzQ6+rNY0lS2H/2pu0ctQJrSPwA8HhEnSNoHuB4YDywDPhQRL0naEbiarL/mGeDUiPhlUz9NAcp8ITarlQu36OBa5hqeNWfYoCLp/UO8tK8kIqKR/MrnAauAyoZOlwKXR8T1kr4GzCHr9J0DbIyIfSWdls7r+FyYdv4FLfOF20vB1YpVr6Zy4jCvBXWStkt6M/Be4G+BC5RtWPtu4Ix0ygKyIeoryZKJ/XU6fhNwhSRFh/e7bOdf0DJfuMceMIkVj2/i2AOc382GN2xQiYhW+02+DHwC2CU9Hw88GxGvpOfVCcO2JROLiFckbUrnP139gSkp2TkAkydPbrF45VLmfoU7Vq5n4+aXuWPles44rLd+75aves2fP4mI/yPpgsFej4gvDfPeE4CnImJZSvCei15OJlbmfoUyBzwrl3rNn7HpfpdBXqt3Qc8A3ifpeGAnsj6VuWQ5kken2kp1wrBKMrHHJI0GdiXrsLUGFdknU+aAZ+VSL6jcDhARn6t9IdVEhhQRFwMXp3OPBP4qIj4o6UbgZLIRoNpkYh8GfpJe/26n+1O6TZn7ZKx/1Jumf5ekKbUHJZ1NVusYiU+SddquI+szmZ+OzwfGp+MXABeN8PP7Vpm2E6jl9Tz9Y9hkYqnp8mXgvRGxNh27mGz05riIeKwtpRyCk4mVV21TzAnBestwycSGralExO1kq5O/I+kASV8mG2ae2emAYq0puuZQuyiwnbUo14o6q+4q5YhYTDYl//vAW4B3R4T/tUqglYun6JXAtUGknds2eJVzZ9UbUv4N2SiPyHbPnwU8lSaxRT+mPS3TWqBWOmaLHiLu5GiRh787q17zZ5eIeEO6f31EjK163ncBBVr7K9hszaLe+a00KXppw6fa31Ptz+bmUHs5y2CTWrmQmw1I9c7vpcDQinq/p9rXHWSK1ejWB5a0Uq1vtlru9TaNqfd7rX3d83mKNeyQctn1+pCyh2EH12q/Vpn6xbrVcEPKrqmUmDscB9dqTcNLDorloFJi/s8/ODcLy80dtTaoMndmVm/DYOXjoFKgMl+Y9TQ7UtXOn7XMa5zMzZ9CdfMoQ7P9Oe38Wd0sLDcHlQJ1c0drsxduN/+sli83f1o0XLU/78lp7WxiNPtdnsVqFQ4qLWqm76H2Qmv2wmvnQrlWv8uL+vqXmz8taqbaX9vv0Gw/RDubGLXf1eyEMTeH+ldhM2ol7QTcQ7a6eTRwU0RckmcysW6bUVt7YeY9s7PImaKe3WvVOjWjdgvZ3ivPS9oB+JGk75BtFdk1ycTyVNv5mfcoRpEjMK55WKMK61OJzPPp6Q7pFmTJxG5KxxcAJ6XHs9Nz0uuz0r4t1qAi5294RbQ1qtCOWkmjJC0HngLuAn5Og8nEgEoysdrPPEfSgKSBDRs2FFn8rlPvwveIjLVDoUElIrZGxEFk+X0OBfbP4TPnRcT0iJg+ceLElstYpGuXPsrBn1/EtUsf7XRRAI/IWHu0ZUg5Ip4Fvge8k5RMLL00WDIxeiWZ2GV3rmbj5pe57M7VnS4K4Ont1h6FBRVJEyXtlh7vDLwHWEUWXE5Opw2WTAx6JJnYhcfsz7gxO3DhMS1X0HIxXPOo1Tk0ZhVFjv5MAhZIGkUWvBZGxG2SHgSul/Q3wP1sn0zsGymZ2K+B0wos2zZFDsOecdjkrklm3uocGrOKwoJKRDwAHDzI8YfJ+ldqj78InFJUeYbSyYun3ryVdu5QVjtk7CFkG6m+n1HbyYunXu2gkyt/+3klsLebbE3fB5Uy5afJu7bgi2Nk3PRrTd8HlU7q5hm2vcxNv9Y4qJRYq0GhlYujn2s5/dz0y4ODSom1+hezlYvDtRwbKe+nYsB/npfiiXI2Ug4qOctz0lgnN2VqdgGhJ8tZhZs/Ocuz2dDODsNWc+l84bYHWf6rZ3nuxVf493Nn5Fw66yauqeSsmWZDvb/urW430EztYeG9j7Jx88ssvHeEix8rKyq6e2WF5cBBpUW1F24zgaDo5k0zn//CS1u3u2/WZ058BzOnTuAzJ75jRO+33uHmT4taae4U3bxp5vPHvn7UdvfN8jCsVbim0qLhmjutNm9a3Y+lmVqTaxqWl8I2vm6Hsm983epm0Qd/fhEbN7/MuDE7cP9njy6ghCN37dJHuezO1Vx4zP5dsxLb8jPcxteuqRSoXqdtvZpMO/djaXZIuEwbUHk4u1wcVApUr/lRryP1jMMmc/9nj264JlDv4hru9S/c9iD3rH2aL9z2YEPfVaYNqLxNZrm4o7aD8u6orddpPOzrTQ4Jl2kDKi8ALJfCgoqkvciSg+1BlppjXkTMlbQ7cAMwBfgl8IGI2JjSccwFjgc2A2dFxH1Fla8M8h4xqXdxDff6Z058x7YFhN3GI0/lUmSGwknApIi4T9IuZNkITwLOAn4dEV+UdBEwLiI+Kel44M/JgsphwNyIGPZ/Sh4dtf28GtdspDrSURsR6ys1jYj4Ddmm13uyfdKw2mRiV6ckZEvIdt0f2ZzxJrSzPe4OResHbemolTSFbL/apcAeEbE+vfQEWfMIqpKJJdWJxqo/K9dkYu1cjVsvgDnoWC8ovKNW0u8ANwPnR8Rz1ZlMIyIkNdX+ioh5wDzImj+tlq+d7fF6fR7ew8R6QaFBJSVmvxm4JiJuSYeflDQpItan5s1T6fi2ZGJJdaKxnlAvgHkUw3pBkcnERJbLZ1VEfKnqpeqkYbXJxM5U5nBgU1UzqTSKbKI4Cbr1giJrKjOADwErUpJ2gE8BXwQWSpoDPAJ8IL12O9nIzzqyIeWzCyzbiLmJYja8IpOJ/QjQEC/PGuT8AM4tqjx5cRPFbHiept+kTjZRPDpk3cBBpYt4SNq6gdf+dBEPSVs3cFDpIh6Stm7goNJDvLDOysB9KmaWKwcVM8uVg0rOmhmB8WiN9SIHlZw1s5VCs9su9HIQ6uWfrd84qOSsma0Uas+td2H18l6svfyz9RuP/uSsmRGY2nPrzTPp5SHjXv7Z+o3z/pSIt7a0buG8PyVRdEL2PMtiNlIOKm1Upn6DMpXFeov7VNqoTP0GZSqL9Rb3qfQw99FYUTrSpyLpf0t6StLKqmO7S7pL0tp0Py4dl6SvSFon6QFJhxRVrm7WbD9IM00c97FYXorsU7kKOLbm2EXA4oiYCixOzwGOA6am2znAlQWWq2s12w/SzJwZ97FYXorcTvKelO+n2mzgyPR4AfB94JNUJRIDlkjarbLjflHlK6N6zZVm+0GamTPjPhbLS7tHf1pKJAb5JxMrk3q1hdoh5zybLN7J3/LSsSHlVCtpupc4IuZFxPSImD5x4sQCSpafZi/6ZrMlusliZdTuoPJkJT9yPyQSa/aib7Ym0s6UrWaNandQ6epEYs1q9aJvtjlkVgaFddRKuo6sU3aCpMeAS+jyRGLNanV7R3eeWjfy5Dcza5oXFJpZ2ziomFmuHFTMLFcOKmaWKwcVM8uVg4qZ5cpBxcxy1dXzVCRtIJtEVzYTgKc7XYghuGwj47Jtb++IGHTxXVcHlbKSNDDUxKBOc9lGxmVrnJs/ZpYrBxUzy5WDSjHmdboAw3DZRsZla5D7VMwsV66pmFmuHFTMLFcOKi2SdJ6klZJ+Jun8dGzQ/EZtKEtpcy0NUbZT0u/tVUnTa86/OJXtIUnHdKBsl0lanX43t0rarURl+0Iq13JJiyS9KR0vR/6siPBthDfgAGAlMIZsF727gX2BfwAuSudcBFzapvLMBA4BVlYdG7QsZDvtfQcQcDiwtANlezvwNrJULdOrjv8e8FNgR2Af4OfAqDaX7WhgdHp8adXvrQxle0PV448DX+vEv+lQN9dUWvN2sn+4zRHxCvAD4P1keYwWpHMWACe1ozARcQ/w65rDQ5VlW66liFgC7FbZlLxdZYuIVRHx0CCnzwauj4gtEfELsm1GD21z2Ralf1OAJWSbsZelbM9VPR3La1kp2vpvOhQHldasBI6QNF7SGLK/FHsxdH6jTmg511IHlK1sHyGrAUBJyibpbyX9Cvgg8Nkylc1BpQURsYqsarwIuANYDmytOWdE+Y2KUKaydAtJnwZeAa7pdFmqRcSnI2IvsnJ9rNPlqeag0qKImB8R0yJiJrARWMPQ+Y06oRtzLZWibJLOAk4APpgCMpSkbFWuAf44PS5F2RxUWiTpjel+Mll/yrUMnd+oE7ox19K3gNMk7ShpH2Aq8B/tLICkY4FPAO+LiM0lK9vUqqezgdVVZev8v2kneod76Qb8EHiQbERgVjo2HlgMrCUbEdq9TWW5DlgPvEzWnp4zVFnIRgj+mWz0YgVVoy9tLNt/TY+3AE8Cd1ad/+lUtoeA4zpQtnVk/RPL0+1rJSrbzWT9eQ8A3wb27MS/6VA3T9M3s1y5+WNmuXJQMbNcOaiYWa4cVMwsVw4qZpYrB5UeIukkSSFp/06XpR5Jv5Q0oer5kZJuS4/PkrQhrcJdLekvhvmckyR9Nj2+StLJBZT1BEmfz/tze5WDSm85HfhRum+ZpFF5fM4I3RARBwEzgE9L2muI8z4BfLXgsvxf4MS0vsvqcFDpEZJ+B/hDsslRp6Vjx0q6seqc6trA0ZJ+Iuk+STem91dqEJdKug84RdJHJd0r6aeSbq5cWJLeKmmJpBWS/kbS81Xfc2F6zwOSPtfKzxURz5BNRPtPq20l7QdsiYjqnDdHSRqQtEbSCem8syR9U9L3le0rc0k6PiXVhK5K518j6ShJP07nHZrKEGTbM5zQys/SLxxUesds4I6IWAM8I2ka2QzawySNTeecClyfmh3/EzgqIg4BBoALqj7rmYg4JCKuB26JiD+IiAOBVWRBC2AuMDcifp9spieQBSuyqeuHAgcB0yTNHKLM30tNnOXA1wc7IS1/2Ils9mitGcB9NcempO9+L/A1STul44eSrZH5L2TBsrIp1L7APwH7p9sZZMH5r4BPVX3uAHDEED+HVXFQ6R2nA9enx9cDp0e2H8gdZFX30WQX2jfJNvD5PeDH6YL+MLB31WfdUPX4AEk/lLSCbJn9O9LxdwKVWtC1VecfnW73k13w+5MFmcG8KyIOSs2cP6157VRJD5DVUr4aES8O8v5JwIaaYwsj4tWIWAs8nL4f4K6IeCYifgvcQhY4AH4RESsi4lXgZ8DiVDNZQRagKp4C3jTEz2FVRne6ANY6SbsD7wZ+X1IAo4CQdCFZgPkY2UY/AxHxG0kiu8iG6nt5oerxVcBJEfHTtGr3yHrFAf4+Iv5lpD9PckNEfCzVKBZJ+lZEPFFzzm+BXWuO1a47iTrHt1Qde7Xq+atsf33slL7P6nBNpTecDHwjIvaOiCmR7bPxC7Lq+g/ItiP8KK/VZJYAMyTtCyBpbOqfGMwuwHpJO5DVVCqW8NqS+9Oqjt8JfKSqj2bPykrukYiIAeAbwHmDvLyKrPlS7RRJr5P0VuAtZIv+AN6jbL/encl2v/txk0XZj2wRn9XhoNIbTgdurTl2M1kTaCtwG3BcuiciNgBnAdelJsZPeK2ZUOszwFKyi3B11fHzgQvS+/cFNqXPXkTWHPpJajLdRBaYWnEpcLak2s+5Bzg41bwqHiXbiuA7wH+rajb9B9nv5AHg5hSsmvEuslEgq8OrlG1E0ijQbyMiJJ1GFsBmd6Acc4FvR8Tdw5xzFtk2ACPaIU3SHsC1ETFrZKXsL+5TsZGaBlyRagnPku3j2gl/BxxW8HdMBv6y4O/oGa6pmFmu3KdiZrlyUDGzXDmomFmuHFTMLFcOKmaWq/8P932WmscJ/58AAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["walking = df[df['sport'] == 'walking']\n", "plt.scatter(walking['heartRateAvg'], walking['kiloCalories'], s=2)\n", "plt.ylabel('Kilocalories')\n", "plt.xlabel('Average HR (bpm)')\n", "plt.savefig('./img/walks_kilocalories_vs_avg_hr.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Regression"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data preparation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we proceed to build a regression model to predict `kiloCalories` burned during a workout. First we create a subset of the original data."]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["reg_df = df[['kiloCalories', 'totalTime', \n", "             'heartRateQ99', 'isStrength', 'sport']].copy()"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>kiloCalories</th>\n", "      <th>totalTime</th>\n", "      <th>heartRateQ99</th>\n", "      <th>isStrength</th>\n", "      <th>sport</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>245.00</td>\n", "      <td>33.33</td>\n", "      <td>115.00</td>\n", "      <td>False</td>\n", "      <td>walking</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>401.00</td>\n", "      <td>46.52</td>\n", "      <td>131.00</td>\n", "      <td>False</td>\n", "      <td>walking</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>336.00</td>\n", "      <td>45.15</td>\n", "      <td>120.00</td>\n", "      <td>False</td>\n", "      <td>walking</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>380.00</td>\n", "      <td>47.65</td>\n", "      <td>124.00</td>\n", "      <td>False</td>\n", "      <td>walking</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>358.00</td>\n", "      <td>39.67</td>\n", "      <td>141.00</td>\n", "      <td>False</td>\n", "      <td>walking</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   kiloCalories  totalTime  heartRateQ99  isStrength    sport\n", "0        245.00      33.33        115.00       False  walking\n", "1        401.00      46.52        131.00       False  walking\n", "2        336.00      45.15        120.00       False  walking\n", "3        380.00      47.65        124.00       False  walking\n", "4        358.00      39.67        141.00       False  walking"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["reg_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We remove the rows where `sport` is `running` because there were only two workouts recorded during the period in question."]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["reg_df = reg_df[reg_df['sport'] != 'running']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outliers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The data is cleansed of outliers using interquartile range."]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["def is_outlier_iqr(series, k=1.5):\n", "    \"\"\"\n", "    Check if value is an outlier\n", "    using interquartile range.\n", "    \"\"\"\n", "    \n", "    q1 = series.quantile(0.25)\n", "    q3 = series.quantile(0.75)        \n", "    iqr = q3 - q1\n", "    is_outlier = (series <= q1 - k * iqr) | (q3 + k * iqr <= series)\n", "    \n", "    return is_outlier"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["time_mask = is_outlier_iqr(series=reg_df['totalTime'])\n", "kcal_mask = is_outlier_iqr(series=reg_df['kiloCalories'])\n", "hr_mask = is_outlier_iqr(series=reg_df['heartRateQ99'])"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["reg_df = reg_df[~(time_mask | kcal_mask | hr_mask)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Histograms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We proceed to visualize histograms of each of the variables."]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.hist(reg_df['kiloCalories'], bins=30)\n", "plt.xlabel('Kilocalories')\n", "plt.ylabel('Frequency')\n", "plt.savefig('./img/kilocalories_histogram.png')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.hist(reg_df['totalTime'], bins=30)\n", "plt.xlabel('Duration (minutes)')\n", "plt.ylabel('Frequency')\n", "plt.savefig('./img/duration_histogram.png')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.hist(reg_df['heartRateQ99'], bins=30)\n", "plt.xlabel('0.99 quantile of heart rate (bpm)')\n", "plt.ylabel('Frequency')\n", "plt.savefig('./img/q99_hr_histogram.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scatter plots"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The plot below gives reason to suspect a linear relationship between `kiloCalories` and `totalTime`."]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["for val in [Fals<PERSON>, True]:\n", "    tmp = reg_df[reg_df['isStrength'] == val]\n", "    plt.scatter(tmp['totalTime'], \n", "                tmp['kiloCalories'],\n", "                s=3, \n", "                label=val)\n", "    \n", "plt.xlabel('Time (minutes)')    \n", "plt.ylabel('Kilocalories')    \n", "plt.legend(title='isStrength', loc='best')\n", "plt.tight_layout()\n", "plt.savefig('./img/time_vs_kilocalories_scatter_by_strength.png')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["for val in [Fals<PERSON>, True]:\n", "    tmp = reg_df[reg_df['isStrength'] == val]\n", "    plt.scatter(tmp['heartRateQ99'],\n", "                tmp['kiloCalories'],\n", "                s=3, \n", "                label=val)\n", "\n", "plt.xlabel('0.99 quantile of heart rate (bpm)')    \n", "plt.ylabel('Kilocalories')\n", "plt.legend(title='isStrength', loc='best')\n", "plt.savefig('./img/99q_hr_vs_kilocalories_scatter_by_strength.png')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.scatter(reg_df['isStrength'] + np.random.normal(scale=1/20, size=len(reg_df)), \n", "            reg_df['kiloCalories'], s=3)\n", "\n", "plt.ylabel('Kilocalories')\n", "plt.xticks(ticks=[0, 1], labels=['Cardio', 'Strength'])\n", "plt.savefig('./img/is_strength_vs_kilocalories_jitter.png')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.scatter(reg_df['isStrength'] + np.random.normal(scale=1/20, size=len(reg_df)), \n", "            reg_df['heartRateQ99'], s=3)\n", "\n", "plt.ylabel('0.99 quantile of heart rate (bpm)')\n", "plt.xticks(ticks=[0, 1], labels=['Cardio', 'Strength'])\n", "plt.savefig('./img/is_strength_vs_99q_hr_scatter.png')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAARUAAAD4CAYAAADCQ3IKAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAe6UlEQVR4nO3dfZRcdZ3n8fcntKRBSWtLGxNgk8YgDJMDqH1ImPExcUXUCZnR4TBRBMc9rGeVTETPSI6cNeuyAzrjqMFRhzO64O7E6DhDjA8xwyaI6yxpTQiBAFF7iZGnSLORBBM70OS7f9S9nZtKVXVV9a2Hrv68zqnTVbdu3fp1Uv2t3+/7e1JEYGaWl2mtLoCZdRYHFTPLlYOKmeXKQcXMcuWgYma56mp1ASbi1FNPjblz57a6GGZTzrZt256KiL5Sz03qoDJ37ly2bt3a6mKYTTmS9pR7zs0fM8uVg4qZ5cpBxcxy5aBiZrlyUDGzXDmomFmuHFTMLFcOKhlrBvew8MZNrBks2wVvZuNwUMlYvXmIvftHuHnzUKuLYjZpOahkLF80j1k93VyzaF6ri2I2aU3qYfp5W7ZgDssWzGl1McwmNddUzCxXDipmlisHFTPLlYOKmeXKQcXMcuWgYma5alhQkfRVSU9K2pk59teSdkm6T9Ltkl6ceW6lpCFJP5N0caPKZWaN1ciayq3AW4uO3QHMj4jzgJ8DKwEknQtcDvx+8povSjqhgWUzswZpWFCJiB8B+4qO/WtEjCYPtwCnJ/cvBdZGxOGI2A0MARc2qmxm1jitzKn8ObAhuX8a8EjmuUeTY8eRdLWkrZK2Dg8PN7iIZlarlgQVSR8HRoF/rPW1EXFLRAxExEBfX8kdAsyshZo+90fSVcA7gMUREcnhx4AzMqednhwzs0mmqTUVSW8F/hJYEhGHMk+tBy6XNF1SP3AW8JNmls3M8tGwmoqkrwNvBE6V9CjwCQq9PdOBOyQBbImID0TEA5K+CTxIoVn0wYh4vlFlM7PG0dEWyOQzMDAQ3qHQrPkkbYuIgVLPeUStmeXKQcXMcuWgYma5clAxs1w5qJhZrhxUzCxXDipmlisHFTPLlYOKmeXKQcXMcuWgYma5clAxs1w5qJhZrhxUzCxXDipmlisHFTPLlYOKmeXKQcWsza0Z*****************************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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.scatter(reg_df['isStrength'] + np.random.normal(scale=1/20, size=len(reg_df)), \n", "            reg_df['totalTime'], s=3)\n", "\n", "plt.ylabel('Time (minutes)')\n", "plt.xticks(ticks=[0, 1], labels=['Cardio', 'Strength'])\n", "plt.savefig('./img/is_strength_vs_time_jitter.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Correlation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We convert binary the feature `isStrength` to integers for the rest of the analysis."]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["reg_df['isStrength'] = reg_df['isStrength'].astype(int)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We inspect the correlation matrix to check for multicollinearity. It should be noted that the correlation between `kiloCalories` and `totalTime` is quite high and this to be expected."]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>kiloCalories</th>\n", "      <th>totalTime</th>\n", "      <th>heartRateQ99</th>\n", "      <th>isStrength</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>kiloCalories</th>\n", "      <td>1.00</td>\n", "      <td>0.92</td>\n", "      <td>0.51</td>\n", "      <td>0.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>totalTime</th>\n", "      <td>0.92</td>\n", "      <td>1.00</td>\n", "      <td>0.28</td>\n", "      <td>0.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>heartRateQ99</th>\n", "      <td>0.51</td>\n", "      <td>0.28</td>\n", "      <td>1.00</td>\n", "      <td>0.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>isStrength</th>\n", "      <td>0.55</td>\n", "      <td>0.69</td>\n", "      <td>0.26</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              kiloCalories  totalTime  heartRateQ99  isStrength\n", "kiloCalories          1.00       0.92          0.51        0.55\n", "totalTime             0.92       1.00          0.28        0.69\n", "heartRateQ99          0.51       0.28          1.00        0.26\n", "isStrength            0.55       0.69          0.26        1.00"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["C = reg_df.corr(method='pearson')\n", "# C = C.style.background_gradient(cmap='YlGn')\n", "# C = C.set_precision(2)\n", "C"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Multicollinearity"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We inspect the respect variance inflation factors and are happy to see that all are below 10."]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Variable</th>\n", "      <th>VIF</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>totalTime</td>\n", "      <td>6.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>heartRateQ99</td>\n", "      <td>3.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>isStrength</td>\n", "      <td>2.41</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Variable  VIF\n", "0     totalTime 6.30\n", "1  heartRateQ99 3.90\n", "2    isStrength 2.41"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["tmp = reg_df.drop(['kiloCalories', 'sport'], axis=1)\n", "\n", "vifs = []\n", "for i in range(tmp.shape[1]):\n", "    vif = variance_inflation_factor(tmp.to_numpy(), i)\n", "    vifs.append(round(vif, 2))\n", "    \n", "vifs = pd.DataFrame(vifs, index=tmp.columns, columns=['VIF'])\n", "vifs = vifs.sort_values('VIF', ascending=False)\n", "vifs = vifs.reset_index()\n", "vifs = vifs.rename(columns={'index': 'Variable'})\n", "\n", "# vifs = vifs.style.background_gradient(cmap='OrRd')\n", "# vifs = vifs.set_precision(2)\n", "\n", "vifs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Modelling"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before the actual modelling we prepare a function to calculate `RMSE` to compare models and extract the true `kiloCalories` into a separate array."]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["y_true = reg_df['kiloCalories'].to_numpy()"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [], "source": ["def calc_rmse(y_true, y_pred):\n", "    x = np.sqrt(np.mean(np.power(y_true - y_pred, 2)))\n", "    return round(x, 4)"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["all_results = []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Time only"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We start the modelling section of by building the simplest model that comes to mind: predict `kiloCalories` using `totalTime`."]}, {"cell_type": "code", "execution_count": 72, "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<tr>\n", "        <td>Model:</td>               <td>OLS</td>         <td>Adj. R-squared:</td>     <td>0.849</td>  \n", "</tr>\n", "<tr>\n", "  <td>Dependent Variable:</td>   <td>kiloCalories</td>          <td>AIC:</td>         <td>3070.3883</td>\n", "</tr>\n", "<tr>\n", "         <td>Date:</td>        <td>2020-10-06 14:50</td>        <td>BIC:</td>         <td>3077.5477</td>\n", "</tr>\n", "<tr>\n", "   <td>No. Observations:</td>         <td>265</td>         <td>Log-Likelihood:</td>    <td>-1533.2</td> \n", "</tr>\n", "<tr>\n", "       <td>Df Model:</td>              <td>1</td>           <td>F-statistic:</td>       <td>1483.</td>  \n", "</tr>\n", "<tr>\n", "     <td>Df Residuals:</td>           <td>263</td>       <td>Prob (F-statistic):</td> <td>4.24e-110</td>\n", "</tr>\n", "<tr>\n", "      <td>R-squared:</td>            <td>0.849</td>            <td>Scale:</td>         <td>6254.0</td>  \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "      <td></td>       <th>Coef.</th>  <th>Std.Err.</th>    <th>t</th>     <th>P>|t|</th> <th>[0.025</th>  <th>0.975]</th> \n", "</tr>\n", "<tr>\n", "  <th>Intercept</th> <td>17.1713</td>  <td>8.7686</td>  <td>1.9583</td>  <td>0.0513</td> <td>-0.0942</td> <td>34.4369</td>\n", "</tr>\n", "<tr>\n", "  <th>totalTime</th> <td>6.7722</td>   <td>0.1759</td>  <td>38.5076</td> <td>0.0000</td> <td>6.4260</td>  <td>7.1185</td> \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "     <td>Omnibus:</td>    <td>35.699</td>  <td><PERSON><PERSON><PERSON><PERSON><PERSON>:</td>    <td>1.803</td>\n", "</tr>\n", "<tr>\n", "  <td>Prob(Omnibus):</td>  <td>0.000</td> <td>Jar<PERSON><PERSON>Bera (JB):</td> <td>92.425</td>\n", "</tr>\n", "<tr>\n", "       <td>Skew:</td>      <td>0.605</td>     <td>Prob(JB):</td>      <td>0.000</td>\n", "</tr>\n", "<tr>\n", "     <td>Kurtosis:</td>    <td>5.628</td>  <td>Condition No.:</td>     <td>90</td>  \n", "</tr>\n", "</table>"], "text/plain": ["<class 'statsmodels.iolib.summary2.Summary'>\n", "\"\"\"\n", "                 Results: Ordinary least squares\n", "==================================================================\n", "Model:              OLS              Adj. R-squared:     0.849    \n", "Dependent Variable: kiloCalories     AIC:                3070.3883\n", "Date:               2020-10-06 14:50 BIC:                3077.5477\n", "No. Observations:   265              Log-Likelihood:     -1533.2  \n", "Df Model:           1                F-statistic:        1483.    \n", "Df Residuals:       263              Prob (F-statistic): 4.24e-110\n", "R-squared:          0.849            Scale:              6254.0   \n", "-------------------------------------------------------------------\n", "                Coef.   Std.Err.     t     P>|t|    [0.025   0.975]\n", "-------------------------------------------------------------------\n", "Intercept      17.1713    8.7686   1.9583  0.0513  -0.0942  34.4369\n", "totalTime       6.7722    0.1759  38.5076  0.0000   6.4260   7.1185\n", "------------------------------------------------------------------\n", "Omnibus:               35.699       <PERSON><PERSON><PERSON>-Watson:          1.803 \n", "Prob(Omnibus):         0.000        Jarque-Bera (JB):       92.425\n", "Skew:                  0.605        Prob(JB):               0.000 \n", "Kurtosis:              5.628        Condition No.:          90    \n", "==================================================================\n", "\n", "\"\"\""]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["formula = 'kiloCalories ~ totalTime'\n", "mdl_time = smf.ols(formula=formula, data=reg_df)\n", "mdl_time = mdl_time.fit()\n", "mdl_time.summary2()"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["y_pred = mdl_time.predict(reg_df)\n", "rmse = calc_rmse(y_pred, y_true)\n", "all_results.append((rmse, formula))"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["78.7832\n"]}], "source": ["print(rmse)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### By sport"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The next regression we are going to do will be univariate regression separately for each sport, this will help us answer the question which sport is the most effective at burning calories during a workout."]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Formula</th>\n", "      <th>Sport</th>\n", "      <th>Intercept</th>\n", "      <th>Slope</th>\n", "      <th>R squared</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>kiloCalories ~ totalTime</td>\n", "      <td>treadmill_running</td>\n", "      <td>-21.23</td>\n", "      <td>10.14</td>\n", "      <td>0.96</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>kiloCalories ~ totalTime</td>\n", "      <td>cycling</td>\n", "      <td>-9.73</td>\n", "      <td>7.44</td>\n", "      <td>0.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>kiloCalories ~ totalTime</td>\n", "      <td>walking</td>\n", "      <td>12.59</td>\n", "      <td>6.95</td>\n", "      <td>0.82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>kiloCalories ~ totalTime</td>\n", "      <td>strength_training</td>\n", "      <td>-12.73</td>\n", "      <td>6.76</td>\n", "      <td>0.44</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    Formula              Sport  Intercept  Slope  R squared\n", "0  kiloCalories ~ totalTime  treadmill_running     -21.23  10.14       0.96\n", "1  kiloCalories ~ totalTime            cycling      -9.73   7.44       0.98\n", "2  kiloCalories ~ totalTime            walking      12.59   6.95       0.82\n", "3  kiloCalories ~ totalTime  strength_training     -12.73   6.76       0.44"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["all_sports = sorted(reg_df['sport'].unique())\n", "reg_sports_res = []\n", "\n", "# For all sport do simple linear regression\n", "for sport in all_sports:\n", "    tmp = reg_df[reg_df['sport'] == sport]\n", "    formula = 'kiloCalories ~ totalTime'\n", "    mdl_sport = smf.ols(formula=formula, data=tmp)\n", "    mdl_sport = mdl_sport.fit()\n", "    sport_stats = [formula, sport] + list(mdl_sport.params) + [mdl_sport.rsquared]    \n", "    reg_sports_res.append(sport_stats)\n", "    \n", "cols = ['Formula', 'Sport', 'Intercept', 'Slope', 'R squared']\n", "\n", "reg_sports_res = pd.DataFrame(reg_sports_res, columns=cols)    \n", "reg_sports_res = reg_sports_res.sort_values(['Slope'], ascending=False)\n", "reg_sports_res = reg_sports_res.reset_index(drop=True)\n", "\n", "readme_df = reg_sports_res.copy().round(2)\n", "\n", "# reg_sports_res = reg_sports_res.style.background_gradient(cmap='YlGn', subset='Slope')\n", "# reg_sports_res = reg_sports_res.set_precision(2)\n", "\n", "reg_sports_res"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Time and heart rate"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We try to enhance the model by adding `heartRateQ99`."]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<tr>\n", "        <td>Model:</td>               <td>OLS</td>         <td>Adj. R-squared:</td>     <td>0.918</td>  \n", "</tr>\n", "<tr>\n", "  <td>Dependent Variable:</td>   <td>kiloCalories</td>          <td>AIC:</td>         <td>2910.3348</td>\n", "</tr>\n", "<tr>\n", "         <td>Date:</td>        <td>2020-10-06 14:50</td>        <td>BIC:</td>         <td>2921.0740</td>\n", "</tr>\n", "<tr>\n", "   <td>No. Observations:</td>         <td>265</td>         <td>Log-Likelihood:</td>    <td>-1452.2</td> \n", "</tr>\n", "<tr>\n", "       <td>Df Model:</td>              <td>2</td>           <td>F-statistic:</td>       <td>1472.</td>  \n", "</tr>\n", "<tr>\n", "     <td>Df Residuals:</td>           <td>262</td>       <td>Prob (F-statistic):</td> <td>3.32e-143</td>\n", "</tr>\n", "<tr>\n", "      <td>R-squared:</td>            <td>0.918</td>            <td>Scale:</td>         <td>3405.9</td>  \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "        <td></td>         <th>Coef.</th>   <th>Std.Err.</th>     <th>t</th>     <th>P>|t|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>Intercept</th>    <td>-420.7741</td>  <td>30.1664</td> <td>-13.9484</td> <td>0.0000</td> <td>-480.1736</td> <td>-361.3746</td>\n", "</tr>\n", "<tr>\n", "  <th>totalTime</th>     <td>6.2025</td>    <td>0.1353</td>   <td>45.8339</td> <td>0.0000</td>  <td>5.9361</td>    <td>6.4690</td>  \n", "</tr>\n", "<tr>\n", "  <th>heartRateQ99</th>  <td>3.7435</td>    <td>0.2519</td>   <td>14.8636</td> <td>0.0000</td>  <td>3.2476</td>    <td>4.2394</td>  \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "     <td>Omnibus:</td>    <td>14.259</td>  <td><PERSON><PERSON><PERSON><PERSON><PERSON>:</td>    <td>1.917</td>\n", "</tr>\n", "<tr>\n", "  <td>Prob(Omnibus):</td>  <td>0.001</td> <td>J<PERSON><PERSON>-Bera (JB):</td> <td>21.717</td>\n", "</tr>\n", "<tr>\n", "       <td>Skew:</td>     <td>-0.355</td>     <td>Prob(JB):</td>      <td>0.000</td>\n", "</tr>\n", "<tr>\n", "     <td>Kurtosis:</td>    <td>4.210</td>  <td>Condition No.:</td>    <td>1106</td> \n", "</tr>\n", "</table>"], "text/plain": ["<class 'statsmodels.iolib.summary2.Summary'>\n", "\"\"\"\n", "                  Results: Ordinary least squares\n", "===================================================================\n", "Model:               OLS              Adj. R-squared:     0.918    \n", "Dependent Variable:  kiloCalories     AIC:                2910.3348\n", "Date:                2020-10-06 14:50 BIC:                2921.0740\n", "No. Observations:    265              Log-Likelihood:     -1452.2  \n", "Df Model:            2                F-statistic:        1472.    \n", "Df Residuals:        262              Prob (F-statistic): 3.32e-143\n", "R-squared:           0.918            Scale:              3405.9   \n", "-------------------------------------------------------------------\n", "               Coef.   Std.Err.    t     P>|t|    [0.025    0.975] \n", "-------------------------------------------------------------------\n", "Intercept    -420.7741  30.1664 -13.9484 0.0000 -480.1736 -361.3746\n", "totalTime       6.2025   0.1353  45.8339 0.0000    5.9361    6.4690\n", "heartRateQ99    3.7435   0.2519  14.8636 0.0000    3.2476    4.2394\n", "-------------------------------------------------------------------\n", "Omnibus:              14.259        <PERSON><PERSON><PERSON>-Watson:           1.917 \n", "Prob(Omnibus):        0.001         Jarque-Bera (JB):        21.717\n", "Skew:                 -0.355        Prob(JB):                0.000 \n", "Kurtosis:             4.210         Condition No.:           1106  \n", "===================================================================\n", "* The condition number is large (1e+03). This might indicate\n", "strong multicollinearity or other numerical problems.\n", "\"\"\""]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["formula = 'kiloCalories ~ totalTime + heartRateQ99'\n", "mdl_time_and_hr = smf.ols(formula=formula, data=reg_df)\n", "mdl_time_and_hr = mdl_time_and_hr.fit()\n", "mdl_time_and_hr.summary2()"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["y_pred = mdl_time_and_hr.predict(reg_df)\n", "rmse = calc_rmse(y_pred, y_true)\n", "all_results.append((rmse, formula))"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["58.0287\n"]}], "source": ["print(rmse)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Time with random effects by workout type"]}, {"cell_type": "code", "execution_count": 79, "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<table class=\"simpletable\">\n", "<tr>\n", "       <td>Model:</td>       <td>MixedLM</td> <td>Dependent Variable:</td> <td>kiloCalories</td>\n", "</tr>\n", "<tr>\n", "  <td>No. Observations:</td>   <td>265</td>         <td>Method:</td>           <td>REML</td>    \n", "</tr>\n", "<tr>\n", "     <td>No. Groups:</td>       <td>2</td>          <td>Scale:</td>          <td>2570.3031</td> \n", "</tr>\n", "<tr>\n", "  <td>Min. group size:</td>    <td>61</td>      <td>Log-Likelihood:</td>    <td>-1416.8825</td> \n", "</tr>\n", "<tr>\n", "  <td>Max. group size:</td>    <td>204</td>       <td>Converged:</td>           <td>Yes</td>    \n", "</tr>\n", "<tr>\n", "  <td>Mean group size:</td>   <td>132.5</td>           <td></td>                 <td></td>      \n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "            <td></td>              <th>Coef.</th>  <th>Std.Err.</th>    <th>z</th>   <th>P>|z|</th>  <th>[0.025</th>   <th>0.975]</th> \n", "</tr>\n", "<tr>\n", "  <th>Intercept</th>             <td>-475.991</td>  <td>65.174</td>  <td>-7.303</td> <td>0.000</td> <td>-603.730</td> <td>-348.252</td>\n", "</tr>\n", "<tr>\n", "  <th>totalTime</th>               <td>6.755</td>    <td>0.858</td>   <td>7.874</td> <td>0.000</td>   <td>5.074</td>    <td>8.437</td> \n", "</tr>\n", "<tr>\n", "  <th>heartRateQ99</th>            <td>3.933</td>    <td>0.220</td>  <td>17.868</td> <td>0.000</td>   <td>3.502</td>    <td>4.365</td> \n", "</tr>\n", "<tr>\n", "  <th>Group Var</th>              <td>210.604</td>  <td>51.671</td>     <td></td>      <td></td>        <td></td>         <td></td>    \n", "</tr>\n", "<tr>\n", "  <th>Group x totalTime Cov</th>   <td>9.904</td>    <td>0.550</td>     <td></td>      <td></td>        <td></td>         <td></td>    \n", "</tr>\n", "<tr>\n", "  <th>totalTime Var</th>           <td>0.466</td>    <td>0.036</td>     <td></td>      <td></td>        <td></td>         <td></td>    \n", "</tr>\n", "</table>"], "text/plain": ["<class 'statsmodels.iolib.summary2.Summary'>\n", "\"\"\"\n", "                Mixed Linear Model Regression Results\n", "======================================================================\n", "Model:                MixedLM     Dependent Variable:     kiloCalories\n", "No. Observations:     265         Method:                 REML        \n", "No. Groups:           2           Scale:                  2570.3031   \n", "Min. group size:      61          Log-Likelihood:         -1416.8825  \n", "Max. group size:      204         Converged:              Yes         \n", "Mean group size:      132.5                                           \n", "----------------------------------------------------------------------\n", "                       Coef.   Std.Err.   z    P>|z|  [0.025   0.975] \n", "----------------------------------------------------------------------\n", "Intercept             -475.991   65.174 -7.303 0.000 -603.730 -348.252\n", "totalTime                6.755    0.858  7.874 0.000    5.074    8.437\n", "heartRateQ99             3.933    0.220 17.868 0.000    3.502    4.365\n", "Group Var              210.604   51.671                               \n", "Group x totalTime Cov    9.904    0.550                               \n", "totalTime Var            0.466    0.036                               \n", "======================================================================\n", "\n", "\"\"\""]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["formula = 'kiloCalories ~ totalTime + heartRateQ99'\n", "re_formula = ' ~ totalTime'\n", "group = 'isStrength'\n", "\n", "mdl_time_with_hr_re = smf.mixedlm(formula=formula, \n", "                  data=reg_df, \n", "                  groups=reg_df[group], \n", "                  re_formula=re_formula)\n", "\n", "mdl_time_with_hr_re = mdl_time_with_hr_re.fit(method='lbfgs')\n", "mdl_time_with_hr_re.summary()"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["y_pred = mdl_time_with_hr_re.predict(reg_df)\n", "rmse = calc_rmse(y_pred, y_true)\n", "all_results.append((rmse, formula, re_formula, group))"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["60.9247\n"]}], "source": ["print(rmse)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model evaluation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We compare the linear models created earlier:"]}, {"cell_type": "code", "execution_count": 82, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RMSE</th>\n", "      <th>Formula</th>\n", "      <th>Random effects</th>\n", "      <th>Groups</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>58.03</td>\n", "      <td>kiloCalories ~ totalTime + heartRateQ99</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>60.92</td>\n", "      <td>kiloCalories ~ totalTime + heartRateQ99</td>\n", "      <td>~ totalTime</td>\n", "      <td>isStrength</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>78.78</td>\n", "      <td>kiloCalories ~ totalTime</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   RMSE                                  Formula Random effects      Groups\n", "1 58.03  kiloCalories ~ totalTime + heartRateQ99           None        None\n", "2 60.92  kiloCalories ~ totalTime + heartRateQ99    ~ totalTime  isStrength\n", "0 78.78                 kiloCalories ~ totalTime           None        None"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["comp_df = pd.DataFrame(all_results, columns=['RMSE', 'Formula', 'Random effects', 'Groups'])\n", "comp_df = comp_df.sort_values('RMSE')\n", "\n", "# comp_df = comp_df.style.background_gradient(cmap='OrRd', subset='RMSE')\n", "# comp_df = comp_df.set_precision(2)\n", "\n", "comp_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For further evaluation we choose the random effects model."]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [], "source": ["mdl = mdl_time_with_hr_re\n", "residuals = mdl_time_with_hr_re.resid"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visual inspection"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We proceed to inspect the residuals of the model. First we view the histogram of the residuals. It can be seen that it looks normal."]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.hist(residuals)\n", "plt.ylabel('Frequency')\n", "plt.xlabel('Residuals')\n", "plt.savefig('./img/mdl_residuals.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The next plot is a qqplot created to visually inspect the normality of the residuals. We see 3 nasty outliers in the top right corner."]}, {"cell_type": "code", "execution_count": 85, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure()\n", "ax = plt.gca()\n", "\n", "qqplot(data=mdl.resid, \n", "       ax=ax, \n", "       color='#1f77b4', \n", "       markersize=3, \n", "       line='45', \n", "       fit=True, \n", "       alpha=1/2)\n", "\n", "plt.savefig('./img/mdl_qq.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The third plot we make is a plot of the standardized residuals to check for homoskedasticity. Again we see the same outliers as on the plot above."]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["residuals_std = np.abs((residuals - np.mean(residuals)) / np.std(residuals))\n", "plt.plot(residuals_std, 'o', markersize=2)\n", "plt.xlabel('Observation')\n", "plt.ylabel('Standardized residuals')\n", "plt.savefig('./img/mdl_residuals_std.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally we compare the predicted `kiloCalories` with the actual values."]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["y_pred = mdl.predict(reg_df)\n", "y_pred = y_pred.to_numpy().reshape(len(y_pred))\n", "\n", "m = np.min(np.hstack([y_true, y_pred]))\n", "M = np.max(np.hstack([y_true, y_pred]))\n", "\n", "x = np.linspace(m, M, len(y_pred))\n", "plt.plot(y_true, y_pred, 'o', markersize=2)\n", "plt.plot(x,x, alpha=3/4)\n", "plt.ylabel('Predicted')\n", "plt.xlabel('Actual')\n", "plt.tight_layout()\n", "plt.savefig('./img/mdl_predicted_vs_actual.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The next step is to take a look at the data points with the biggest error. As can be seen the model has issues predicting strength training workouts."]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "    #T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row0_col2 {\n", "            background-color:  #7f0000;\n", "            color:  #f1f1f1;\n", "        }    #T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row1_col2 {\n", "            background-color:  #fdce98;\n", "            color:  #000000;\n", "        }    #T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row2_col2 {\n", "            background-color:  #fee4bf;\n", "            color:  #000000;\n", "        }    #T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row3_col2 {\n", "            background-color:  #fee6c4;\n", "            color:  #000000;\n", "        }    #T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row4_col2 {\n", "            background-color:  #fff7ec;\n", "            color:  #000000;\n", "        }</style><table id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39\" ><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >kiloCaloriesPredicted</th>        <th class=\"col_heading level0 col1\" >kiloCalories</th>        <th class=\"col_heading level0 col2\" >error</th>        <th class=\"col_heading level0 col3\" >totalTime</th>        <th class=\"col_heading level0 col4\" >isStrength</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row0_col0\" class=\"data row0 col0\" >573.76</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row0_col1\" class=\"data row0 col1\" >338.00</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row0_col2\" class=\"data row0 col2\" >235.76</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row0_col3\" class=\"data row0 col3\" >91.35</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row0_col4\" class=\"data row0 col4\" >1</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row1_col0\" class=\"data row1 col0\" >546.98</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row1_col1\" class=\"data row1 col1\" >373.00</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row1_col2\" class=\"data row1 col2\" >173.98</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row1_col3\" class=\"data row1 col3\" >78.07</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row1_col4\" class=\"data row1 col4\" >1</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row2_col0\" class=\"data row2 col0\" >631.69</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row2_col1\" class=\"data row2 col1\" >795.00</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row2_col2\" class=\"data row2 col2\" >163.31</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row2_col3\" class=\"data row2 col3\" >69.65</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row2_col4\" class=\"data row2 col4\" >0</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row3_col0\" class=\"data row3 col0\" >562.00</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row3_col1\" class=\"data row3 col1\" >400.00</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row3_col2\" class=\"data row3 col2\" >162.00</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row3_col3\" class=\"data row3 col3\" >76.80</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row3_col4\" class=\"data row3 col4\" >1</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row4_col0\" class=\"data row4 col0\" >412.98</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row4_col1\" class=\"data row4 col1\" >263.00</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row4_col2\" class=\"data row4 col2\" >149.98</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row4_col3\" class=\"data row4 col3\" >67.55</td>\n", "                        <td id=\"T_93eceb2c_07d2_11eb_ae80_2d8d84982c39row4_col4\" class=\"data row4 col4\" >1</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7fd58c7d6400>"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["errors = reg_df.copy()\n", "errors['kiloCaloriesPredicted'] = mdl.predict(reg_df)\n", "\n", "errors['error'] = np.abs(errors['kiloCalories'] - errors['kiloCaloriesPredicted'])\n", "\n", "errors = errors.sort_values('error', ascending=False)\n", "errors = errors.reset_index(drop=True)\n", "\n", "order = ['kiloCaloriesPredicted',\n", "         'kiloCalories', \n", "         'error',\n", "         'totalTime',\n", "         'isStrength']\n", "\n", "errors = errors[order]\n", "\n", "errors = errors.head(5)\n", "\n", "errors = errors.style.background_gradient(cmap='OrRd', subset='error')\n", "errors = errors.set_precision(2)\n", "\n", "errors"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Summary"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [], "source": ["# Make table for README\n", "# print(tabulate.tabulate(by_sport.values, by_sport.columns, tablefmt=\"pipe\"))"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [], "source": ["# Make table for README\n", "# print(tabulate.tabulate(readme_df.values, readme_df.columns, tablefmt=\"pipe\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* In this project I define a `workout` as each instance in time when my watch was recording me.\n", "\n", "* I downloaded data generated by my Polar watch that tracks `heart rate` and estimates burned `kilocalories` during workouts.\n", "\n", "* The data came in the form of `.json` files which were read, transformed and cleaned with `pandas`. \n", "\n", "* The clean dataset contains `283` workouts over a nearly one year period during which I burned roughly `12kg` of body fat.\n", "\n", "| Sport             |   Total kilocalories |   Total kilograms |\n", "|:------------------|---------------------:|------------------:|\n", "| walking           |                33080 |              4.3  |\n", "| strength_training |                31547 |              4.1  |\n", "| treadmill_running |                19825 |              2.57 |\n", "| cycling           |                 4029 |              0.52 |\n", "| running           |                  940 |              0.12 |\n", "\n", "* The timing of my workouts appears to follow a `bimodal distribution` with peaks at `12:00` and `20:00`.\n", "\n", "<!-- ![image](https://github.com/besiobu/data-science-portfolio/blob/master/polar/img/workouts_by_hour_of_day.png) -->\n", "\n", "* After further transforming the data, I find that the `duration` of a workout and `kilocalorie`'s burned have a `0.92` correlation. \n", "\n", "<!-- ![image](https://github.com/besiobu/data-science-portfolio/blob/master/polar/img/time_vs_kilocalories_scatter_by_strength.png) -->\n", "\n", "* Several linear regressions were performed. \n", "\n", "* `kilocalories ~ duration` on the entire dataset achieved `R^2 = 0.85` and `RMSE = 79`.\n", "\n", "* Regressions were performed on subsets of the data, specifically by sport - the highest slope is `10.14 kiloCalories` per minute.\n", "\n", "| Formula                  | Sport             |   Intercept |   Slope |   R squared |\n", "|:-------------------------|:------------------|------------:|--------:|------------:|\n", "| kilo_calories ~ total_time | treadmill_running |      -21.23 |   10.14 |        0.96 |\n", "| kilo_calories ~ total_time | cycling           |       -9.73 |    7.44 |        0.98 |\n", "| kilo_calories ~ total_time | walking           |       12.59 |    6.95 |        0.82 |\n", "| kilo_calories ~ total_time | strength_training |      -12.73 |    6.76 |        0.44 |\n", "\n", "* A `linear mixed model with random effects` was created and validated. It achieved a `RMSE = 61` and normal looking residuals.\n", "\n", "<!-- ![image](https://github.com/besiobu/data-science-portfolio/blob/master/polar/img/mdl_predicted_vs_actual.png) -->\n", "\n", "* The biggest `errors` made by the `mixed model` was on `strength training` data points."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.2"}}, "nbformat": 4, "nbformat_minor": 4}