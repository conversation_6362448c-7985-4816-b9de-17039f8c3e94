{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# flats-in-cracow machine learning"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from distutils.dir_util import copy_tree\n", "from pathlib import Path\n", "\n", "import joblib\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from matplotlib.ticker import MaxNLocator\n", "from pylab import rcParams\n", "from sklearn.compose import ColumnTransformer, TransformedTargetRegressor\n", "from sklearn.dummy import DummyRegressor\n", "from sklearn.ensemble import (GradientBoostingRegressor, RandomForestRegressor,\n", "                              VotingRegressor)\n", "from sklearn.impute import KNNImputer\n", "from sklearn.metrics import (mean_absolute_error, mean_squared_error,\n", "                             mean_squared_log_error)\n", "from sklearn.model_selection import GridSearchCV, train_test_split, KFold\n", "from sklearn.neural_network import MLPRegressor\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.preprocessing import MinMaxScaler, OneHotEncoder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Create directory for images \n", "Path(\"img\").mkdir(parents=True, exist_ok=True)\n", "\n", "# Set default figure size\n", "rcParams['figure.figsize'] = (4, 4)\n", "\n", "# Tell pandas how to display floats\n", "pd.options.display.float_format = \"{:,.2f}\".format"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["path = '../flats-data/cleaned_data.csv'"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["data = pd.read_csv(path, lineterminator='\\n')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 4592 entries, 0 to 4591\n", "Data columns (total 17 columns):\n", " #   Column     Non-Null Count  Dtype \n", "---  ------     --------------  ----- \n", " 0   District   4592 non-null   object\n", " 1   Amount     4592 non-null   int64 \n", " 2   Seller     4592 non-null   object\n", " 3   Area       4592 non-null   int64 \n", " 4   Rooms      4592 non-null   int64 \n", " 5   Bathrooms  4592 non-null   int64 \n", " 6   Parking    4592 non-null   object\n", " 7   Garden     4592 non-null   bool  \n", " 8   Balcony    4592 non-null   bool  \n", " 9   Terrace    4592 non-null   bool  \n", " 10  Floor      4592 non-null   bool  \n", " 11  New        4592 non-null   bool  \n", " 12  Estate     4592 non-null   bool  \n", " 13  Townhouse  4592 non-null   bool  \n", " 14  Apartment  4592 non-null   bool  \n", " 15  Land       4592 non-null   bool  \n", " 16  Studio     4592 non-null   bool  \n", "dtypes: bool(10), int64(4), object(3)\n", "memory usage: 296.1+ KB\n"]}], "source": ["data.info()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>District</th>\n", "      <th>Amount</th>\n", "      <th>Seller</th>\n", "      <th>Area</th>\n", "      <th>Rooms</th>\n", "      <th>Bathrooms</th>\n", "      <th>Parking</th>\n", "      <th>Garden</th>\n", "      <th>Balcony</th>\n", "      <th>Terrace</th>\n", "      <th>Floor</th>\n", "      <th>New</th>\n", "      <th>Estate</th>\n", "      <th>Townhouse</th>\n", "      <th>Apartment</th>\n", "      <th>Land</th>\n", "      <th>Studio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>595000</td>\n", "      <td>realtor</td>\n", "      <td>78</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>podgorze</td>\n", "      <td>449000</td>\n", "      <td>realtor</td>\n", "      <td>61</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>nowa huta</td>\n", "      <td>449000</td>\n", "      <td>realtor</td>\n", "      <td>58</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>595000</td>\n", "      <td>realtor</td>\n", "      <td>78</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>430000</td>\n", "      <td>realtor</td>\n", "      <td>48</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>garage</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    District  Amount   Seller  Area  Rooms  Bathrooms     Parking  Garden  \\\n", "0  krowodrza  595000  realtor    78      4          2  no parking   False   \n", "1   podgorze  449000  realtor    61      3          1  no parking   False   \n", "2  nowa huta  449000  realtor    58      3          1  no parking   False   \n", "3  krowodrza  595000  realtor    78      4          2  no parking   False   \n", "4  krowodrza  430000  realtor    48      2          1      garage   False   \n", "\n", "   Balcony  Terrace  Floor    New  Estate  Townhouse  Apartment   Land  Studio  \n", "0     True    False  False  False   False      False      False  False   False  \n", "1     True    False   True  False   False      False      False  False   False  \n", "2     True    False  False   True   False      False      False  False   False  \n", "3     True    False  False  False   False      False      False  False   False  \n", "4     True    False   True  False    True      False      False  False   False  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Feature engineering"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The next step is to engineer features. We add columns describing the `Total Rooms` in the property, ratio of `Area to Rooms` and so on."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["data['Log Area'] = np.round(np.log(data['Area']), 2)\n", "data['Bool Sum'] = data.select_dtypes(bool).sum(axis=1)\n", "# Avoid division by zero\n", "data['Area to Bool Sum'] = round(data['Area'] / (data.select_dtypes(bool).sum(axis=1) + 1), 2)\n", "data['Rooms to Bool Sum'] = round(data['Rooms'] / (data.select_dtypes(bool).sum(axis=1) + 1), 2)\n", "data['Rooms to Bathrooms' ] = round(data['Rooms'] / data['Bathrooms'], 2)\n", "data['Total Rooms'] = round(data['Rooms'] + data['Bathrooms'], 2)\n", "data['Area to Rooms'] = round(data['Area'] / data['Rooms'], 2)\n", "data['Area to Bathrooms'] = round(data['Area'] / data['Rooms'], 2)\n", "data['Area to Total Rooms'] = round(data['Area'] / data['Total Rooms'], 2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data split"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We decide to use $80\\%$ of the data to train the model and $20\\%$ to check performance.\n", "We make sure to remove the `Amount` column from the training data since this is our target and remove duplicates before training."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4592\n", "3998\n"]}], "source": ["print(len(data))\n", "data = data.drop_duplicates()\n", "print(len(data))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["X = data.drop(['Amount'], axis=1)\n", "y = data['Amount']\n", "\n", "split = train_test_split(X, y, train_size=.8,\n", "                               random_state=123)\n", "\n", "X_train, X_test, y_train, y_test = split"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next step is to create the models and associated piplines. We apply one hot encoding to categorical features and use the `ColumnTransformer` parameter `passthrough` to allow the rest of the columns to remain unchanged."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["categorical = list(X.select_dtypes('object').columns)    \n", "continuous = list(X.select_dtypes('int64'))\n", "continuous += list(X.select_dtypes('float64'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Baseline model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For comparison purposes we create a model to give base predictions."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["dmr = DummyRegressor()\n", "\n", "dmr_ohe = Pipeline(\n", "    steps=[('onehot', OneHotEncoder(handle_unknown='ignore'))]\n", ")\n", "\n", "dmr = Pipeline(steps = [('preprocessor', dmr_ohe),\n", "                        ('regressor', dmr)])\n", "\n", "# dmr.fit(X_train, y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Multi-layer Perceptron"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For the neural network we apply the `MinMaxScaler` so that the continuous columns have values in $[0, 1]$ and then we apply `OneHotEncoder` to the categorical columns."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["mlp = MLPRegressor(hidden_layer_sizes=(100, 100, 100), \n", "                   max_iter=2*10**4, \n", "                   random_state=123)\n", "\n", "mlp_ohe = Pipeline(\n", "    steps=[('onehot', OneHotEncoder(handle_unknown='ignore'))]\n", ")\n", "\n", "mlp_scale = Pipeline(\n", "    steps=[('scale', MinMaxScaler())]\n", ")\n", "\n", "mlp_pre = ColumnTransformer(\n", "    transformers = [\n", "        ('scale', mlp_scale, continuous),        \n", "        ('cat', mlp_ohe, categorical),\n", "    ],\n", "    remainder='passthrough'\n", ")\n", "\n", "mlp_trans = TransformedTargetRegressor(regressor=mlp, \n", "                                       transformer=MinMaxScaler())\n", "\n", "mlp = Pipeline(steps = [('preprocessor', mlp_pre),\n", "                        ('transformer', mlp_trans)])\n", "\n", "# mlp.fit(X_train, y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Gradient Boosting Regressor"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For the gradient booster we only apply `OneHotEncoder` to the categorical columns."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["gbr = GradientBoostingRegressor(random_state=123)\n", "\n", "gbr_ohe = Pipeline(\n", "    steps=[('onehot', OneHotEncoder(handle_unknown='ignore'))]\n", ")\n", "\n", "gbr_pre = ColumnTransformer(\n", "    transformers = [\n", "        ('cat', gbr_ohe, categorical)\n", "    ],\n", "    remainder='passthrough'\n", ")\n", "\n", "gbr = Pipeline(steps = [('preprocessor', gbr_pre),\n", "                        ('regressor', gbr)])\n", "\n", "# gbr.fit(X_train, y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameter tuning"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We set up the training process to conduct basic parameter tuning and cross validation."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["kf = KFold(n_splits=5, random_state=123, shuffle=True)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["gbr_grid = {'regressor__max_depth': [5, 10, 15], \n", "            'regressor__n_estimators': [50, 100, 200, 300],\n", "            'regressor__min_samples_split': [2, 4],\n", "            'regressor__min_samples_leaf': [2, 4],\n", "            'regressor__max_features': ['auto']}\n", "\n", "gbr_gs = GridSearchCV(estimator=gbr, \n", "                      param_grid=gbr_grid,\n", "                      cv=kf,\n", "                      n_jobs=8,\n", "                      scoring='neg_root_mean_squared_error',\n", "                      verbose=2)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["layers = [(100, 100, 100),\n", "          (150, 200, 150), \n", "          (200, 400, 200)]\n", "\n", "mlp_grid = {'transformer__regressor__activation': ['relu'],\n", "            'transformer__regressor__solver': ['adam'],\n", "            'transformer__regressor__learning_rate': ['adaptive'],\n", "            'transformer__regressor__learning_rate_init': [0.01, 0.001, 0.0001],\n", "            'transformer__regressor__hidden_layer_sizes': layers}\n", "\n", "mlp_gs = GridSearchCV(estimator=mlp, \n", "                      param_grid=mlp_grid,\n", "                      cv=kf,\n", "                      n_jobs=8,\n", "                      scoring='neg_root_mean_squared_error',                      \n", "                      verbose=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["Pipeline(steps=[('preprocessor',\n", "                 Pipeline(steps=[('onehot',\n", "                                  OneHotEncoder(handle_unknown='ignore'))])),\n", "                ('regressor', <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>())])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["dmr.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fitting 5 folds for each of 9 candidates, totalling 45 fits\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=8)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=8)]: Done  25 tasks      | elapsed:   25.5s\n", "[Parallel(n_jobs=8)]: Done  45 out of  45 | elapsed:  1.2min finished\n"]}, {"data": {"text/plain": ["Pipeline(steps=[('preprocessor',\n", "                 ColumnTransformer(remainder='passthrough',\n", "                                   transformers=[('scale',\n", "                                                  Pipeline(steps=[('scale',\n", "                                                                   MinMaxScaler())]),\n", "                                                  ['Area', 'Rooms', 'Bathrooms',\n", "                                                   'Bool Sum', 'Total Rooms',\n", "                                                   'Log Area',\n", "                                                   'Area to Bool Sum',\n", "                                                   'Rooms to Bool Sum',\n", "                                                   'Rooms to Bathrooms',\n", "                                                   'Area to Rooms',\n", "                                                   'Area to Bathrooms',\n", "                                                   'Area to Total Rooms']),\n", "                                                 ('cat',\n", "                                                  Pipeline(steps=[('onehot',\n", "                                                                   OneHotEncoder(handle_unknown='ignore'))]),\n", "                                                  ['District', 'Seller',\n", "                                                   'Parking'])])),\n", "                ('transformer',\n", "                 TransformedTargetRegressor(regressor=MLPRegressor(hidden_layer_sizes=(200,\n", "                                                                                       400,\n", "                                                                                       200),\n", "                                                                   learning_rate='adaptive',\n", "                                                                   learning_rate_init=0.0001,\n", "                                                                   max_iter=20000,\n", "                                                                   random_state=123),\n", "                                            transformer=MinMaxScaler()))])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["mlp = mlp_gs.fit(X_train, y_train).best_estimator_\n", "mlp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["CV `RMSE` score for `MLPRegressor`:"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["119689\n"]}], "source": ["print(round(abs(mlp_gs.best_score_)))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fitting 5 folds for each of 48 candidates, totalling 240 fits\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=8)]: Using backend LokyBackend with 8 concurrent workers.\n", "[Parallel(n_jobs=8)]: Done  25 tasks      | elapsed:    9.7s\n", "[Parallel(n_jobs=8)]: Done 146 tasks      | elapsed:  1.4min\n", "[Parallel(n_jobs=8)]: Done 240 out of 240 | elapsed:  3.5min finished\n"]}, {"data": {"text/plain": ["Pipeline(steps=[('preprocessor',\n", "                 ColumnTransformer(remainder='passthrough',\n", "                                   transformers=[('cat',\n", "                                                  Pipeline(steps=[('onehot',\n", "                                                                   OneHotEncoder(handle_unknown='ignore'))]),\n", "                                                  ['District', 'Seller',\n", "                                                   'Parking'])])),\n", "                ('regressor',\n", "                 GradientBoostingRegressor(max_depth=5, max_features='auto',\n", "                                           min_samples_leaf=4,\n", "                                           random_state=123))])"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["gbr = gbr_gs.fit(X_train, y_train).best_estimator_\n", "gbr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["CV `RMSE` score for `GradientBoostingRegressor`:"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["117912\n"]}], "source": ["print(round(abs(gbr_gs.best_score_)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Voting Regressor"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We create a `VotingRegressor` with uniform weights to be able to combine predictions of our models."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["vote = VotingRegressor(estimators=[['mlp', mlp], ['gbr', gbr]], n_jobs=8)\n", "vote = vote.fit(X_train, y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model performance"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We obtain predictions for the testing set and compare `RMSE`, `MAE` and `MSLE` scores of our models."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["def get_scores(regressor, X_test, y_true, verb=True):\n", "    \"\"\"\n", "    Obtain RMSE, MAE and MSLE for test set.\n", "    \"\"\"\n", "    \n", "    y_pred = regressor.predict(X_test)\n", "    \n", "    rmse = mean_squared_error(y_pred=y_pred, \n", "                              y_true=y_test, \n", "                              squared=False)\n", "    \n", "    mae = mean_absolute_error(y_pred=y_pred, \n", "                              y_true=y_test)\n", "    \n", "    msle = mean_squared_log_error(y_pred=y_pred, \n", "                                  y_true=y_test)    \n", "    \n", "    if verb:\n", "        \n", "        print(f'RMSE: {rmse:10.2f}')\n", "        print(f'MAE:  {mae:10.2f}')\n", "        print(f'MSLE: {msle:10.2f}')        \n", "    \n", "    return (rmse, mae, msle)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Dummy"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RMSE:  222475.70\n", "MAE:   161219.86\n", "MSLE:       0.13\n"]}], "source": ["dmr_score = get_scores(regressor=dmr, \n", "                       X_test=X_test, \n", "                       y_true=y_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Multilayer Perceptrion"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RMSE:  120493.86\n", "MAE:    79646.51\n", "MSLE:       0.03\n"]}], "source": ["mlp_score = get_scores(regressor=mlp, \n", "                       X_test=X_test, \n", "                       y_true=y_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Gradient Boosting Regressor"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RMSE:  119237.05\n", "MAE:    76182.62\n", "MSLE:       0.03\n"]}], "source": ["gbr_score = get_scores(regressor=gbr, \n", "                       X_test=X_test, \n", "                       y_true=y_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Voting Regressor"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RMSE:  116478.02\n", "MAE:    74993.84\n", "MSLE:       0.03\n"]}], "source": ["vote_score = get_scores(regressor=vote, \n", "                        X_test=X_test, \n", "                        y_true=y_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Comparison"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We are happy to see that the `VotingRegressor` outperforms the `DummyRegressor` model as well the `GradientBoostingRegressor` and the `MLPRegressor`."]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RMSE</th>\n", "      <th>MAE</th>\n", "      <th>MSLE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>DMR</th>\n", "      <td>222,475.70</td>\n", "      <td>161,219.86</td>\n", "      <td>0.13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MLP</th>\n", "      <td>120,493.86</td>\n", "      <td>79,646.51</td>\n", "      <td>0.03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>GBR</th>\n", "      <td>119,237.05</td>\n", "      <td>76,182.62</td>\n", "      <td>0.03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>VOTE</th>\n", "      <td>116,478.02</td>\n", "      <td>74,993.84</td>\n", "      <td>0.03</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           RMSE        MAE  MSLE\n", "DMR  222,475.70 161,219.86  0.13\n", "MLP  120,493.86  79,646.51  0.03\n", "GBR  119,237.05  76,182.62  0.03\n", "VOTE 116,478.02  74,993.84  0.03"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["scores = [dmr_score,\n", "          mlp_score,\n", "          gbr_score,\n", "          vote_score]\n", "\n", "scores = pd.DataFrame(scores, \n", "                      index=['DMR', 'MLP', 'GBR', 'VOTE'], \n", "                      columns=['RMSE', 'MAE', 'MSLE'])\n", "\n", "scores"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizations "]}, {"cell_type": "markdown", "metadata": {}, "source": ["We produce a couple of plots the visually inspect the performance of our model.\n", "We use the test data set with the predicted `Amount` to produce the plots."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Amount</th>\n", "      <th>Predicted Amount</th>\n", "      <th>District</th>\n", "      <th>Area</th>\n", "      <th>Total Rooms</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1241</th>\n", "      <td>660000</td>\n", "      <td>657,844.33</td>\n", "      <td>podgorze</td>\n", "      <td>79</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3535</th>\n", "      <td>600000</td>\n", "      <td>607,834.91</td>\n", "      <td>lagiewniki</td>\n", "      <td>67</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2449</th>\n", "      <td>339000</td>\n", "      <td>342,399.17</td>\n", "      <td>br<PERSON><PERSON><PERSON></td>\n", "      <td>30</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2992</th>\n", "      <td>528000</td>\n", "      <td>531,523.50</td>\n", "      <td>czyzyny</td>\n", "      <td>55</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2756</th>\n", "      <td>850000</td>\n", "      <td>776,874.86</td>\n", "      <td>stare miasto</td>\n", "      <td>63</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Amount  Predicted Amount      District  Area  Total Rooms\n", "1241  660000        657,844.33      podgorze    79            5\n", "3535  600000        607,834.91    lagiewniki    67            4\n", "2449  339000        342,399.17     <PERSON><PERSON><PERSON><PERSON>    30            2\n", "2992  528000        531,523.50       czyzyny    55            4\n", "2756  850000        776,874.86  stare miasto    63            3"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["cols = ['Amount', 'Predicted Amount', \n", "        'District', 'Area', 'Total Rooms']\n", "\n", "X_pred = X_test.copy()\n", "X_pred.loc[:, 'Amount'] = y\n", "X_pred.loc[:, 'Predicted Amount'] = vote.predict(X_test)\n", "X_pred = X_pred.loc[:, cols]\n", "\n", "X_pred.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["On our first visual it can be seen that there exists a fairly linear relationship between the `Predicted Amount` and the `Area` of the property."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.scatter(X_pred['Area'], X_pred['Predicted Amount'], s=2)\n", "plt.xlabel('Area')\n", "plt.ylabel('Predicted Amount (PLN)')\n", "\n", "ax = plt.gca()\n", "ax.xaxis.set_major_locator(MaxNLocator(integer=True))\n", "\n", "plt.tight_layout()\n", "plt.savefig('img/area_vs_amount.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["On the second visual it can bee seen, as expected the more `Total Rooms` in a `Property` the more it should cost."]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.scatter(X_pred['Total Rooms'], X_pred['Predicted Amount'], s=2)\n", "plt.xlabel('Total Rooms')\n", "plt.ylabel('Predicted Amount (PLN)')\n", "\n", "ax = plt.gca()\n", "ax.xaxis.set_major_locator(MaxNLocator(integer=True))\n", "\n", "plt.tight_layout()\n", "plt.savefig('img/rooms_vs_amount.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next we want to check if the model distinguishes between districts. We group the data by `District` and calculate the mean of the predictions with the group. We produce a bar chart sorted from highest average to lowest. Clearly the model distinguishes between district that are near the city center (`stare miasto`, `zwierzyniec`) and those further away (`łagiewniki`, `bieżanów`)."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["width = 1600\n", "height = width/2\n", "dpi = 200\n", "\n", "X_grp = X_pred[['District', 'Predicted Amount']]\n", "X_grp = X_grp.groupby('District', as_index=False).mean()\n", "X_grp = X_grp.sort_values('Predicted Amount', ascending=False)\n", "\n", "plt.figure(figsize=(width/dpi, height/dpi))\n", "\n", "plt.bar(X_grp['District'], X_grp['Predicted Amount'] / 1000)\n", "\n", "plt.ylabel('Mean predicted amount (k PLN)')\n", "plt.ylim(X_grp['Predicted Amount'].min() * 0.67 / 1000, None)\n", "plt.xticks(rotation=90)\n", "\n", "plt.tight_layout()\n", "plt.savefig('img/district_vs_avg_amount.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting predictions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next we would like see how the model handles sets of arbitrary parameters. We write a function to transform inputs to desired format and obtain prediction from the model."]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["def get_pred(district,\n", "             seller,\n", "             area,\n", "             rooms,\n", "             bathrooms,\n", "             parking,\n", "             garden,\n", "             balcony,\n", "             terrace,\n", "             floor,\n", "             new,\n", "             estate,\n", "             townhouse,\n", "             apartment,\n", "             land,\n", "             studio):\n", "\n", "    columns = ['District',\n", "               'Seller',\n", "               'Area',\n", "               'Rooms',\n", "               'Bathrooms',\n", "               'Parking',\n", "               'Garden',\n", "               'Balcony',\n", "               'Terrace',\n", "               'Floor',\n", "               'New',\n", "               'Estate',\n", "               'Townhouse',\n", "               'Apartment',\n", "               'Land',\n", "               'Studio',\n", "               'Log Area',\n", "               'Bool Sum',\n", "               'Area to Bool Sum',\n", "               'Rooms to Bool Sum',\n", "               'Rooms to Bathrooms',\n", "               'Total Rooms',\n", "               'Area to Rooms',\n", "               'Area to Bathrooms',\n", "               'Area to Total Rooms']\n", "        \n", "    log_area = np.log(area)\n", "\n", "    all_bools = [garden,\n", "                 balcony,\n", "                 terrace,\n", "                 floor,\n", "                 new,\n", "                 estate,\n", "                 townhouse,\n", "                 apartment,\n", "                 land,\n", "                 studio]\n", "    \n", "    bool_sum = sum(all_bools)\n", "    area_to_bool_sum = area / (bool_sum + 1)\n", "    rooms_to_bool_sum = rooms / (bool_sum + 1)    \n", "    rooms_to_bathrooms = rooms / bathrooms        \n", "    total_rooms = rooms + bathrooms\n", "    area_to_rooms = area / total_rooms\n", "    area_to_bathrooms = area / bathrooms\n", "    area_to_total_rooms = area / total_rooms\n", "    \n", "    x = [district,\n", "         seller,\n", "         area,\n", "         rooms,\n", "         bathrooms,\n", "         parking,\n", "         garden,\n", "         balcony,\n", "         terrace,\n", "         floor,\n", "         new,\n", "         estate,\n", "         townhouse,\n", "         apartment,\n", "         land,\n", "         studio,\n", "         log_area,\n", "         bool_sum,\n", "         area_to_bool_sum,\n", "         rooms_to_bool_sum,\n", "         rooms_to_bathrooms,\n", "         total_rooms,\n", "         area_to_rooms,\n", "         area_to_bathrooms,\n", "         area_to_total_rooms]    \n", "    \n", "    x = pd.DataFrame([x], columns=columns)    \n", "    x = float(vote.predict(x))\n", "\n", "    return int(round(x, -3))    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["We create lists of inputs for the model to predict."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["areas = range(30, 120, 5)\n", "rooms = range(1, 5)\n", "districts = ['stare miasto', \n", "             'bronowice', \n", "             'krow<PERSON><PERSON>a',\n", "             'borek falecki']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next we loop over lists of possible `Area`'s and `Room`'s and plot the outputs. First we check how the model reacts to different districts."]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure()\n", "\n", "for d in districts:\n", "    value = list()\n", "    for a in areas:\n", "        pred = get_pred(district=d,\n", "         seller='realtor',\n", "         area=a,\n", "         rooms=2,\n", "         bathrooms=1,\n", "         parking='street',\n", "         garden=False,\n", "         balcony=False,\n", "         terrace=False,\n", "         floor=False,\n", "         new=True,\n", "         estate=False,\n", "         townhouse=True,\n", "         apartment=False,\n", "         land=False,\n", "         studio=True)\n", "        value.append(pred)\n", "    plt.plot(areas, value, label=d)\n", "    \n", "plt.ylabel('Price (PLN)')\n", "plt.xlabel('Area')\n", "plt.legend(loc='best')\n", "plt.savefig('img/area_vs_amount_by_district')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We do the same for different amounts of `Room`'s."]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"image/png": "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***************************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\n", "text/plain": ["<Figure size 288x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.figure()\n", "\n", "for r in rooms:\n", "    value = list()\n", "    for a in areas:\n", "        pred = get_pred(district='stare miasto',\n", "         seller='owner',\n", "         area=a,\n", "         rooms=r,\n", "         bathrooms=1,\n", "         parking='street',\n", "         garden=False,\n", "         balcony=True,\n", "         terrace=False,\n", "         floor=False,\n", "         new=True,\n", "         estate=False,\n", "         townhouse=True,\n", "         apartment=False,\n", "         land=False,\n", "         studio=True)\n", "        value.append(pred)\n", "    plt.plot(areas, value, label=f'{r} rooms')\n", "    \n", "plt.title('<PERSON><PERSON> Miasto')\n", "plt.ylabel('Price (PLN)')\n", "plt.xlabel('Area')    \n", "plt.legend(loc='best')\n", "plt.savefig('img/area_vs_amount_by_rooms')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Final training"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The last step is to fit the model to the entire dataset and save it for later use."]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Full training took 648 seconds.\n"]}], "source": ["start = datetime.now()\n", "\n", "gbr.fit(X, y)\n", "joblib.dump(gbr, f'../flats-model/gbr.joblib')\n", "\n", "mlp.fit(X, y)\n", "joblib.dump(mlp, f'../flats-model/mlp.joblib')\n", "\n", "vote.fit(X, y)\n", "joblib.dump(vote, f'../flats-model/vote.joblib')\n", "\n", "end = datetime.now()\n", "\n", "duration = (end - start).seconds\n", "\n", "print(f'Full training took {int(duration)} seconds.')"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["# Copy files to portfolio\n", "# fromDirectory = '.'\n", "# toDirectory = '/home/<USER>/Github/data-science-portfolio/flats-in-cracow'\n", "# copy_tree(fromDirectory, toDirectory)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.2"}}, "nbformat": 4, "nbformat_minor": 4}