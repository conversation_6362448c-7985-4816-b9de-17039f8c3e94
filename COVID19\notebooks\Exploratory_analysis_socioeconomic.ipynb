{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "import matplotlib as mpl"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["project_path = os.path.abspath(os.path.join('..'))\n", "\n", "if project_path not in sys.path:\n", "    sys.path.append(f'{project_path}/src/visualizations/')\n", "    \n", "from covid_data_viz import CovidDataViz    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Setup"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["mpl.rcParams['figure.figsize'] = (9, 5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Goal\n", "My goal is to visualize various aspect of the `COVID-19` pandemic."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data sources\n", "\n", "In this notebook I use data from the following sources:\n", "- https://github.com/CSSEGISandData/COVID-19 - JHU CSSE COVID-19 Data.\n", "- [GDP per capita PPP](https://data.worldbank.org/indicator/NY.GDP.PCAP.PP.CD) - The World Bank.\n", "- [Population](https://data.worldbank.org/indicator/SP.POP.TOTL) - The World Bank.\n", "- [Rural population](https://data.worldbank.org/indicator/SP.RUR.TOTL.ZS) - The World Bank.\n", "- [Life expectancy at birth](https://data.worldbank.org/indicator/SP.DYN.LE00.IN) - The World Bank.\n", "- [Current healthcare expenditure](https://data.worldbank.org/indicator/SH.XPD.CHEX.GD.ZS) - The World Bank.\n", "- https://datahub.io/JohnSnowLabs/country-and-continent-codes-list - country codes and continents."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data loading"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["cdv = CovidDataViz()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`Yemen` is an outlier and is excluded from the analysis."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Socioeconomic data.\n", "\n", "To enhance the analysis I used data avaible freely at from the `World Bank`. In this part of the analysis I use the last available value for each country. This is a reasonble thing to do given that these specific do not undergo wild fluctuations from year to year."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Correlation matrix\n", "\n", "Note that `Rural population %` and `Cases per mln` have a correlation of `-0.46%`. Possible reasons could be the virus has a harder time spreading in scarcely populated countries. Note also that `GDP Healthcare` and `Dead per mln` have a positive correlation of `0.38`. This could be considered an expected result given that excluding `Asia` most of the countries around the world do not have experience in dealing with such matters."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<style  type=\"text/css\" >\n", "    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col0 {\n", "            background-color:  #b40426;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col1 {\n", "            background-color:  #f6bea4;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col2 {\n", "            background-color:  #89acfd;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col3 {\n", "            background-color:  #f4c6af;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col4 {\n", "            background-color:  #f2cab5;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col5 {\n", "            background-color:  #5470de;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col6 {\n", "            background-color:  #c0282f;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col7 {\n", "            background-color:  #5b7ae5;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col0 {\n", "            background-color:  #f7b79b;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col1 {\n", "            background-color:  #b40426;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col2 {\n", "            background-color:  #d9dce1;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col3 {\n", "            background-color:  #e3d9d3;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col4 {\n", "            background-color:  #f4c5ad;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col5 {\n", "            background-color:  #f7b194;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col6 {\n", "            background-color:  #e6d7cf;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col7 {\n", "            background-color:  #6e90f2;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col0 {\n", "            background-color:  #b6cefa;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col1 {\n", "            background-color:  #e8d6cc;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col2 {\n", "            background-color:  #b40426;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col3 {\n", "            background-color:  #edd1c2;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col4 {\n", "            background-color:  #f7b194;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col5 {\n", "            background-color:  #cfdaea;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col6 {\n", "            background-color:  #85a8fc;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col7 {\n", "            background-color:  #8fb1fe;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col0 {\n", "            background-color:  #eed0c0;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col1 {\n", "            background-color:  #cbd8ee;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col2 {\n", "            background-color:  #c9d7f0;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col3 {\n", "            background-color:  #b40426;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col4 {\n", "            background-color:  #ec7f63;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col5 {\n", "            background-color:  #8db0fe;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col6 {\n", "            background-color:  #e0dbd8;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col7 {\n", "            background-color:  #3c4ec2;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col0 {\n", "            background-color:  #ead4c8;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col1 {\n", "            background-color:  #ead5c9;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col2 {\n", "            background-color:  #eed0c0;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col3 {\n", "            background-color:  #ec7f63;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col4 {\n", "            background-color:  #b40426;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col5 {\n", "            background-color:  #bad0f8;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col6 {\n", "            background-color:  #d2dbe8;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col7 {\n", "            background-color:  #3b4cc0;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col0 {\n", "            background-color:  #90b2fe;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col1 {\n", "            background-color:  #f6a283;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col2 {\n", "            background-color:  #d4dbe6;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col3 {\n", "            background-color:  #d1dae9;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col4 {\n", "            background-color:  #ead5c9;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col5 {\n", "            background-color:  #b40426;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col6 {\n", "            background-color:  #6a8bef;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col7 {\n", "            background-color:  #a3c2fe;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col0 {\n", "            background-color:  #c0282f;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col1 {\n", "            background-color:  #e4d9d2;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col2 {\n", "            background-color:  #5f7fe8;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col3 {\n", "            background-color:  #efcfbf;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col4 {\n", "            background-color:  #e7d7ce;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col5 {\n", "            background-color:  #3b4cc0;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col6 {\n", "            background-color:  #b40426;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col7 {\n", "            background-color:  #6a8bef;\n", "            color:  #000000;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col0 {\n", "            background-color:  #3b4cc0;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col1 {\n", "            background-color:  #3b4cc0;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col2 {\n", "            background-color:  #3b4cc0;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col3 {\n", "            background-color:  #3b4cc0;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col4 {\n", "            background-color:  #3b4cc0;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col5 {\n", "            background-color:  #4961d2;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col6 {\n", "            background-color:  #3b4cc0;\n", "            color:  #f1f1f1;\n", "        }    #T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col7 {\n", "            background-color:  #b40426;\n", "            color:  #f1f1f1;\n", "        }</style><table id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fd\" style=\"font-size: 13px\"><thead>    <tr>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Cases per mln</th>        <th class=\"col_heading level0 col1\" >Dead per mln</th>        <th class=\"col_heading level0 col2\" >GDP Healthcare %</th>        <th class=\"col_heading level0 col3\" >GDP per capita</th>        <th class=\"col_heading level0 col4\" >Life expectancy</th>        <th class=\"col_heading level0 col5\" >Mortality %</th>        <th class=\"col_heading level0 col6\" >Recovered per mln</th>        <th class=\"col_heading level0 col7\" >Rural population %</th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdlevel0_row0\" class=\"row_heading level0 row0\" >Cases per mln</th>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col0\" class=\"data row0 col0\" >1.00</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col1\" class=\"data row0 col1\" >0.52</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col2\" class=\"data row0 col2\" >0.08</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col3\" class=\"data row0 col3\" >0.38</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col4\" class=\"data row0 col4\" >0.36</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col5\" class=\"data row0 col5\" >-0.08</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col6\" class=\"data row0 col6\" >0.95</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow0_col7\" class=\"data row0 col7\" >-0.46</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdlevel0_row1\" class=\"row_heading level0 row1\" >Dead per mln</th>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col0\" class=\"data row1 col0\" >0.52</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col1\" class=\"data row1 col1\" >1.00</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col2\" class=\"data row1 col2\" >0.38</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col3\" class=\"data row1 col3\" >0.23</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col4\" class=\"data row1 col4\" >0.39</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col5\" class=\"data row1 col5\" >0.63</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col6\" class=\"data row1 col6\" >0.36</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow1_col7\" class=\"data row1 col7\" >-0.37</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdlevel0_row2\" class=\"row_heading level0 row2\" >GDP Healthcare %</th>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col0\" class=\"data row2 col0\" >0.08</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col1\" class=\"data row2 col1\" >0.38</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col2\" class=\"data row2 col2\" >1.00</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col3\" class=\"data row2 col3\" >0.31</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col4\" class=\"data row2 col4\" >0.49</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col5\" class=\"data row2 col5\" >0.35</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col6\" class=\"data row2 col6\" >-0.07</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow2_col7\" class=\"data row2 col7\" >-0.21</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdlevel0_row3\" class=\"row_heading level0 row3\" >GDP per capita</th>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col0\" class=\"data row3 col0\" >0.38</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col1\" class=\"data row3 col1\" >0.23</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col2\" class=\"data row3 col2\" >0.31</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col3\" class=\"data row3 col3\" >1.00</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col4\" class=\"data row3 col4\" >0.69</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col5\" class=\"data row3 col5\" >0.12</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col6\" class=\"data row3 col6\" >0.32</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow3_col7\" class=\"data row3 col7\" >-0.62</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdlevel0_row4\" class=\"row_heading level0 row4\" >Life expectancy</th>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col0\" class=\"data row4 col0\" >0.36</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col1\" class=\"data row4 col1\" >0.39</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col2\" class=\"data row4 col2\" >0.49</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col3\" class=\"data row4 col3\" >0.69</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col4\" class=\"data row4 col4\" >1.00</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col5\" class=\"data row4 col5\" >0.27</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col6\" class=\"data row4 col6\" >0.25</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow4_col7\" class=\"data row4 col7\" >-0.63</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdlevel0_row5\" class=\"row_heading level0 row5\" >Mortality %</th>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col0\" class=\"data row5 col0\" >-0.08</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col1\" class=\"data row5 col1\" >0.63</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col2\" class=\"data row5 col2\" >0.35</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col3\" class=\"data row5 col3\" >0.12</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col4\" class=\"data row5 col4\" >0.27</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col5\" class=\"data row5 col5\" >1.00</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col6\" class=\"data row5 col6\" >-0.18</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow5_col7\" class=\"data row5 col7\" >-0.12</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdlevel0_row6\" class=\"row_heading level0 row6\" >Recovered per mln</th>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col0\" class=\"data row6 col0\" >0.95</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col1\" class=\"data row6 col1\" >0.36</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col2\" class=\"data row6 col2\" >-0.07</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col3\" class=\"data row6 col3\" >0.32</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col4\" class=\"data row6 col4\" >0.25</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col5\" class=\"data row6 col5\" >-0.18</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col6\" class=\"data row6 col6\" >1.00</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow6_col7\" class=\"data row6 col7\" >-0.39</td>\n", "            </tr>\n", "            <tr>\n", "                        <th id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdlevel0_row7\" class=\"row_heading level0 row7\" >Rural population %</th>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col0\" class=\"data row7 col0\" >-0.46</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col1\" class=\"data row7 col1\" >-0.37</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col2\" class=\"data row7 col2\" >-0.21</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col3\" class=\"data row7 col3\" >-0.62</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col4\" class=\"data row7 col4\" >-0.63</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col5\" class=\"data row7 col5\" >-0.12</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col6\" class=\"data row7 col6\" >-0.39</td>\n", "                        <td id=\"T_f9a10098_0732_11eb_a39c_8b9d6530a6fdrow7_col7\" class=\"data row7 col7\" >1.00</td>\n", "            </tr>\n", "    </tbody></table>"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f379578c400>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cdv.show_corr_mat()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Mortality\n", "\n", "As a reminder note that `mortality = dead / confirmed`. Observe that the `Life expectancy` vs. `Mortality %` scatter plot has a few nasty outliers to right of the plot. One possible explanation is that people from the risk group are more prevalent in countries with higher `life expectancy`. In the scatter plots we do not find any nice linear relationships. Perhaps examining the data on a per continent level would yield more fruitfull results."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["cdv.plot_with_slope('Rural population %', 'Mortality %')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["cdv.plot_with_slope('Life expectancy', 'Mortality %')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["cdv.plot_with_slope('GDP Healthcare %', 'Mortality %')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["cdv.plot_with_slope('GDP per capita', 'Mortality %')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.2"}}, "nbformat": 4, "nbformat_minor": 2}