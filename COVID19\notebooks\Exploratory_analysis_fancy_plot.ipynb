{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import numpy as np\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["project_path = os.path.abspath(os.path.join('..'))\n", "\n", "if project_path not in sys.path:\n", "    sys.path.append(f'{project_path}/src/visualizations/')\n", "    \n", "from covid_data_viz import CovidDataViz    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Setup"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["mpl.rcParams['figure.figsize'] = (9, 5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Goal\n", "My goal is to visualize various aspect of the `COVID-19` pandemic."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data sources\n", "\n", "In this project I use data from the following sources:\n", "- https://github.com/CSSEGISandData/COVID-19 - JHU CSSE COVID-19 Data.\n", "- https://datahub.io/JohnSnowLabs/country-and-continent-codes-list - country codes and continents."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data loading"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["cdv = CovidDataViz()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fancy plot\n", "\n", "Visual for repo readme."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAhUAAAEYCAYAAADrkgQ9AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAgAElEQVR4nOydebhcRZn/P99cbhIhYTOAskgQRHEDnajjyqYogyij4AIu4ILgD1FHHZdxGGTcxnEUEVziBoqAiKCAII4ijKiMBlGRAVQw7MgakgCBcPP+/qg63Lonp/v2ze17uzt8P89znj6n6q0676k+fc7bVW/Vq4jAGGOMMWayzOi1AsYYY4xZO7BRYYwxxpiuYKPCGGOMMV3BRoUxxhhjuoKNCmOMMcZ0BRsVxhhjjOkKNiqMMcYY0xVsVJi+QVLk7VpJs1vILM4y60y3fhNF0laSPinpEkl3SVop6VZJP5H0TkkbtCi3vaTjJF0pabmkeyRdJekLkh7fIBuSbpQ0NI4+z8myvy/SqvacX5Ot0qttpaQ7JF0m6VuS9pM0cw3aZDhf+zck/U7SA7n+t4xTbrtc5oZc5uasx7YT1cEYM3XIi1+ZfkFSeTN+MCI+2SCzGNgaGI6IB6dLt4mSX5LHArOA3wO/BO4CHgk8D3gScEdEzKuVOxz4DMngvxC4BAjg74BdgFXAP0XEMUWZC4CdgZdHxJltdPoGcCBwWEQcl9MWk9pzm4hYXMhW6Z8DlmR91gceDzwfWA/4M/C6iPj1BNplw9wOAH8DHgC2At4aEV9tUWYBcD4wF/gpcGnW7R+Be4FdIuLSTnUwxkwhEeHNW19spJfnncAdpBfZvAaZxVlunV7r2+Y6DiiuZa8WMs8FfldLe0MudwfwgoYyz895Aby+SN8/p53ZRqf1geXAPcAGDe05v0U7z2+oawPgmJy/BHjCBNpmJrAn8Oh8fGSu5y1tyvw+y7y7lv484EHgd+Q/SN68eevt5uEP02/cC/w76cX1bxMpKOlZkk6TdEvuIr9e0pclbV6TOzl3uT+uln5CTv9pLX1u7v7/nw50mEt64QK8JiJ+2CQXEb8AnlUrd3Q+*****************************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\n", "text/plain": ["<Figure size 576x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["countries = ['Germany', \n", "             'France', \n", "             'Italy', \n", "             'Spain', \n", "             'United Kingdom', \n", "             'Russia', \n", "             'India', \n", "             'Brazil',\n", "             'US', \n", "             'Poland', \n", "             'Mexico']\n", "\n", "width = 1600\n", "height = width / 2\n", "dpi = 200\n", "period = 7\n", "step = 30\n", "label_size = 12\n", "n_clabels = 6\n", "\n", "countries = sorted(countries)\n", "\n", "plot_df = cdv.data['Confirmed chg'][countries]\n", "plot_df = plot_df.rename(columns={'United Kingdom': 'UK'})\n", "countries = plot_df.columns.to_list()\n", "plot_df = plot_df.rolling(period)\n", "plot_df = plot_df.mean()\n", "plot_df = plot_df.dropna()\n", "plot_df = plot_df.to_numpy()\n", "plot_df = plot_df.astype(float)\n", "plot_df = plot_df.transpose()\n", "plot_df = np.sqrt(plot_df)\n", "\n", "xticks = range(plot_df.shape[1])[::step]\n", "xlabels = list(cdv.data['Confirmed chg']['Date'])[period:]\n", "xlabels = [x.strftime(format='%Y-%m') for x in xlabels]\n", "# xlabels = [x.date() for x in xlabels]\n", "xlabels = xlabels[::step]\n", "\n", "yticks = range(len(countries))\n", "ylabels = countries\n", "\n", "cticks = np.round(np.linspace(0, np.max(plot_df), 6), -1)\n", "cticks = cticks.astype(np.int)\n", "clabels = np.power(cticks, 2)\n", "cticks = sorted(set(cticks))\n", "clabels = np.power(cticks, 2)\n", "clabels = [int((round(x, -3))/1000) for x in clabels]\n", "clabels = [str(x)+'k' for x in clabels]\n", "# clabels = list(map(str, clabels))\n", "\n", "plt.figure(figsize=(width / dpi, height / dpi))\n", "plt.imshow(plot_df, aspect='auto', interpolation='nearest')\n", "plt.set_cmap('hot')\n", "\n", "plt.yticks(ticks=yticks,\n", "           labels=ylabels, \n", "           fontsize=label_size, \n", "           verticalalignment='center')\n", "\n", "plt.xticks(ticks=xticks,\n", "           labels=xlabels,\n", "           rotation=45, \n", "           fontsize=label_size, \n", "           horizontalalignment='center')\n", "\n", "cbar = plt.colorbar()\n", "cbar.set_ticks(cticks)\n", "cbar.set_ticklabels(clabels)\n", "cbar.ax.tick_params(labelsize=label_size) \n", "\n", "plt.title('New COVID-19 cases', fontsize=20)\n", "\n", "plt.tight_layout()\n", "plt.savefig('../img/covid_tiles.png')\n", "\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.2"}}, "nbformat": 4, "nbformat_minor": 2}