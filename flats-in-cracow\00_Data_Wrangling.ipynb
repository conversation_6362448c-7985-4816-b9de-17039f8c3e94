{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# flats-in-cracow data wrangling"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from collections import Counter\n", "from IPython.display import display\n", "from sklearn.impute import KNNImputer\n", "from pylab import rcParams\n", "from pathlib import Path"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Create directory for images \n", "Path(\"img\").mkdir(parents=True, exist_ok=True)\n", "\n", "# Set default figure size\n", "rcParams['figure.figsize'] = (4, 4)\n", "\n", "# Tell pandas how to display floats\n", "pd.options.display.float_format = \"{:,.2f}\".format"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Goal"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I scraped listings of properties for sale in Cracow. We would like to create a model to predict flat prices."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data source"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Data has been scraped from a website with listings. The data has undergone small transformations along the way. The goal of these transformations was to get the data into a usable state not to check it's validity."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["path = '../flats-data/raw_data.csv'"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["data = pd.read_csv(path, lineterminator='\\n')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 60604 entries, 0 to 60603\n", "Data columns (total 24 columns):\n", " #   Column       Non-Null Count  Dtype  \n", "---  ------       --------------  -----  \n", " 0   Date         60434 non-null  object \n", " 1   City         46536 non-null  object \n", " 2   District     33403 non-null  object \n", " 3   Amount       60375 non-null  float64\n", " 4   C<PERSON>rency     60375 non-null  object \n", " 5   Property     60023 non-null  object \n", " 6   Seller       60269 non-null  object \n", " 7   Area         60118 non-null  float64\n", " 8   Rooms        59423 non-null  float64\n", " 9   Bathrooms    38847 non-null  float64\n", " 10  Parking      26133 non-null  object \n", " 11  Garden       60604 non-null  bool   \n", " 12  Balcony      60604 non-null  bool   \n", " 13  Terrace      60604 non-null  bool   \n", " 14  Floor        60604 non-null  bool   \n", " 15  New          60604 non-null  bool   \n", " 16  Estate       60604 non-null  bool   \n", " 17  Townhouse    60604 non-null  bool   \n", " 18  Apartment    60604 non-null  bool   \n", " 19  Land         60604 non-null  bool   \n", " 20  Studio       60604 non-null  bool   \n", " 21  Title        60434 non-null  object \n", " 22  Description  52855 non-null  object \n", " 23  Link         60604 non-null  object \n", "dtypes: bool(10), float64(4), object(10)\n", "memory usage: 7.1+ MB\n"]}], "source": ["data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First we sort the data in from newest to oldest, forcing rows with missing `Date` values to be last."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["data = data.sort_values(by='Date', \n", "                        ascending=False, \n", "                        na_position='last', \n", "                        ignore_index=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next we assume that the `Title` column uniquely identifies a listing."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["data = data.drop_duplicates(['Title'], keep='first')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After this the shape of the data is:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(10484, 24)\n"]}], "source": ["print(data.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data exploration"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We check for missing values that we will have to deal with."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Missing</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Parking</th>\n", "      <td>6604</td>\n", "    </tr>\n", "    <tr>\n", "      <th>District</th>\n", "      <td>4392</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Bathrooms</th>\n", "      <td>4187</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Description</th>\n", "      <td>1900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>City</th>\n", "      <td>1688</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Rooms</th>\n", "      <td>232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Area</th>\n", "      <td>129</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Seller</th>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Property</th>\n", "      <td>83</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Amount</th>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON>cy</th>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Title</th>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Missing\n", "Parking         6604\n", "District        4392\n", "Bathrooms       4187\n", "Description     1900\n", "City            1688\n", "Rooms            232\n", "Area             129\n", "<PERSON><PERSON>            88\n", "Property          83\n", "Amount             9\n", "Currency           9\n", "Date               1\n", "Title              1"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["missing = data.isnull().sum(axis=0)\n", "missing.name = 'Missing'\n", "missing = missing.to_frame()\n", "missing = missing[missing['Missing'] > 0]\n", "missing.sort_values('Missing', ascending=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check numeric columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We see that we have 24 columns at our disposal. \n", "We inspect the numeric columns to see what we are dealing with. \n", "In the `Amount` column we note there is a property for sale that costs 1PLN, clearly a erroneous value. \n", "Next we note that the enourmous maximum in the `Amount` column. That is quite a lot of money and could be considered a potential outlier.\n", "The maximum and minimum of the `Area` column also indicate the existance of outliers. These values are clearly too large. The data will need to undergo a filtering process."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Amount</th>\n", "      <th>Area</th>\n", "      <th>Rooms</th>\n", "      <th>Bathrooms</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>10,475.00</td>\n", "      <td>10,355.00</td>\n", "      <td>10,252.00</td>\n", "      <td>6,297.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>722,001.88</td>\n", "      <td>132.23</td>\n", "      <td>2.92</td>\n", "      <td>1.32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>5,139,332.07</td>\n", "      <td>3,562.58</td>\n", "      <td>1.32</td>\n", "      <td>0.63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>100.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>395,000.00</td>\n", "      <td>43.00</td>\n", "      <td>2.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>499,400.00</td>\n", "      <td>56.00</td>\n", "      <td>3.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>720,000.00</td>\n", "      <td>80.00</td>\n", "      <td>4.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>521,290,000.00</td>\n", "      <td>320,000.00</td>\n", "      <td>6.00</td>\n", "      <td>4.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              Amount       Area     Rooms  Bathrooms\n", "count      10,475.00  10,355.00 10,252.00   6,297.00\n", "mean      722,001.88     132.23      2.92       1.32\n", "std     5,139,332.07   3,562.58      1.32       0.63\n", "min           100.00       1.00      1.00       1.00\n", "25%       395,000.00      43.00      2.00       1.00\n", "50%       499,400.00      56.00      3.00       1.00\n", "75%       720,000.00      80.00      4.00       1.00\n", "max   521,290,000.00 320,000.00      6.00       4.00"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check binary columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We inspect the data to see if binary columns are properly populated and check for imbalances."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Garden</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>8407</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>True</td>\n", "      <td>2077</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Garden  Count\n", "0   False   8407\n", "1    True   2077"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Balcony</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>6816</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>True</td>\n", "      <td>3668</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Balcony  Count\n", "0    False   6816\n", "1     True   3668"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Terrace</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>9237</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>True</td>\n", "      <td>1247</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Terrace  Count\n", "0    False   9237\n", "1     True   1247"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Floor</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>6398</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>True</td>\n", "      <td>4086</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Floor  Count\n", "0  False   6398\n", "1   True   4086"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>New</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>7090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>True</td>\n", "      <td>3394</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     New  Count\n", "0  False   7090\n", "1   True   3394"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Estate</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>8947</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>True</td>\n", "      <td>1537</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Estate  Count\n", "0   False   8947\n", "1    True   1537"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Townhouse</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>9576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>True</td>\n", "      <td>908</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Townhouse  Count\n", "0      False   9576\n", "1       True    908"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Apartment</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>8960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>True</td>\n", "      <td>1524</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Apartment  Count\n", "0      False   8960\n", "1       True   1524"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Land</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>8047</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>True</td>\n", "      <td>2437</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Land  Count\n", "0  False   8047\n", "1   True   2437"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Studio</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>False</td>\n", "      <td>9788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>True</td>\n", "      <td>696</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Studio  Count\n", "0   False   9788\n", "1    True    696"]}, "metadata": {}, "output_type": "display_data"}], "source": ["binary = data.select_dtypes(bool).columns.to_list()\n", "\n", "for col in binary:\n", "    tmp = data[[col, 'Amount']]\n", "    tmp = tmp.fillna('NaN')\n", "    tmp = tmp.groupby(col, as_index=False)\n", "    tmp = tmp.count()\n", "    tmp = tmp.rename(columns={'Amount': 'Count'})\n", "    tmp = tmp.sort_values('Count', ascending=False)\n", "    tmp = tmp.reset_index(drop=True)\n", "    display(tmp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check categorical columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We inspect categorical columns to assert that they contain \"valid\" values. Most of these columns were generated by a script during the scraping and etl phase of the project."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>City</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>kraków</td>\n", "      <td>8796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NaN</td>\n", "      <td>1688</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     City  Count\n", "0  kraków   8796\n", "1     NaN   1688"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>District</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>4392</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>stare miasto</td>\n", "      <td>696</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>podgorze</td>\n", "      <td>641</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>nowa huta</td>\n", "      <td>455</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>deb<PERSON>i</td>\n", "      <td>442</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>br<PERSON><PERSON><PERSON></td>\n", "      <td>435</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>pradnik bialy</td>\n", "      <td>426</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>pradnik czerwony</td>\n", "      <td>323</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>biezanow</td>\n", "      <td>318</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>g<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>306</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>czyzyny</td>\n", "      <td>235</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>mistrzejowice</td>\n", "      <td>203</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>lagiewniki</td>\n", "      <td>171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>zwierzyniec</td>\n", "      <td>151</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>pod<PERSON><PERSON> du<PERSON>e</td>\n", "      <td>132</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>bien<PERSON><PERSON><PERSON></td>\n", "      <td>120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>swoszowice</td>\n", "      <td>106</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>prokocim</td>\n", "      <td>62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td><PERSON><PERSON> falecki</td>\n", "      <td>34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>wzgorza krzeslawickie</td>\n", "      <td>23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 District  Count\n", "0                     NaN   4392\n", "1               krowodrza    813\n", "2            stare miasto    696\n", "3                podgorze    641\n", "4               nowa huta    455\n", "5                 debniki    442\n", "6               br<PERSON><PERSON><PERSON>    435\n", "7           pradnik bialy    426\n", "8        pradnik czerwony    323\n", "9                biezanow    318\n", "10             grzegorzki    306\n", "11                czyzyny    235\n", "12          mistrzejowice    203\n", "13             lagiewniki    171\n", "14            zwierzyniec    151\n", "15     podgorze duchackie    132\n", "16              bienczyce    120\n", "17             swoszowice    106\n", "18               prokocim     62\n", "19          borek falecki     34\n", "20  wzgorza krzeslawickie     23"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON><PERSON>cy</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>pln</td>\n", "      <td>10475</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NaN</td>\n", "      <td>9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Currency  Count\n", "0      pln  10475\n", "1      NaN      9"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Property</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>flat</td>\n", "      <td>9015</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>house</td>\n", "      <td>1386</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>83</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Property  Count\n", "0     flat   9015\n", "1    house   1386\n", "2      NaN     83"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Seller</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>realtor</td>\n", "      <td>9598</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>owner</td>\n", "      <td>798</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>88</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Seller  Count\n", "0  realtor   9598\n", "1    owner    798\n", "2      NaN     88"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Parking</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NaN</td>\n", "      <td>6604</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>street</td>\n", "      <td>1519</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>garage</td>\n", "      <td>1516</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>no parking</td>\n", "      <td>651</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>covered</td>\n", "      <td>194</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Parking  Count\n", "0         NaN   6604\n", "1      street   1519\n", "2      garage   1516\n", "3  no parking    651\n", "4     covered    194"]}, "metadata": {}, "output_type": "display_data"}], "source": ["categorical = data.select_dtypes('object').columns\n", "categorical = categorical.to_list()\n", "omit = ['Title', '<PERSON>', 'Description', 'Date']\n", "\n", "for col in categorical:\n", "    if col not in omit:\n", "        tmp = data[['Amount', col]].copy()\n", "        tmp = tmp.fillna('NaN')\n", "        tmp = tmp.groupby(col, as_index=False)\n", "        tmp = tmp.count()\n", "        tmp = tmp.rename(columns={'Amount': 'Count'})\n", "        tmp = tmp.sort_values('Count', ascending=False)\n", "        tmp = tmp.reset_index(drop=True)\n", "        display(tmp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check text columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We search for keywords in the data."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# text = data[data['Description'].isna() == False].copy()\n", "# text = text['Description'].to_list()\n", "# text = ' '.join(text)\n", "# text = text.split(' ')\n", "# text = [x for x in text if x.isalpha()]\n", "# text = [x for x in text if len(x) > 3]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"scrolled": false}, "outputs": [], "source": ["# for i in range(5, len(text)-5):\n", "#     if 'piętro' in text[i]:    \n", "#         s = text[i-5:i+5]\n", "#         s = ' '.join(s)\n", "#         print(s)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data cleaning"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We assume that if we know the district, the `City` is `kraków`."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["mask = (data['City'].isna() == True) & (data['District'].isna() == False)\n", "data.loc[mask, 'City'] = 'kraków'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We extract more `Parking` information from the property description."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def extract_parking(x):\n", "    if ('garaż' in x or 'garaz' in x or 'parking' in x) and 'podziemny' in x:\n", "        return 'covered'\n", "    elif ('garaż' in x or 'garaz' in x) and 'podziemny' not in x:\n", "        return 'garage'\n", "    elif 'parking' in x and 'podziemny' not in x:\n", "        return 'street'\n", "    else:\n", "        return 'no parking'"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["mask = (data['Parking'].isna() == True) & (data['Description'].isna() == False)\n", "data.loc[mask, ['Parking', 'Description']] = data.loc[mask, 'Description'].apply(extract_parking)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["mask = data['Parking'].isna() == True\n", "data.loc[mask, 'Parking'] = 'no parking'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We confirm that we have dealt with all the `NaN`s in the `Parking` column."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n"]}], "source": ["print(data['Parking'].isna().sum())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Filtering"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next we filter the data according to these rules:"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["data = data[data['City'] == 'kraków']\n", "data = data[data['Currency'] == 'pln']\n", "data = data[data['Property'] == 'flat']\n", "data = data[(data['Amount'] >= data['Amount'].quantile(0.025))]\n", "data = data[(data['Amount'] <= data['Amount'].quantile(0.975))]\n", "data = data[(data['Area'] >= data['Area'].quantile(0.01))]\n", "data = data[(data['Area'] <= data['Area'].quantile(0.99))]\n", "data = data[data['District'] != 'unknown']\n", "data = data[data['District'].isna() == False]\n", "data = data[data['Seller'].isna() == False]\n", "data = data[data['Description'].isna() == False]"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["data = data.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 4592 entries, 0 to 4591\n", "Data columns (total 24 columns):\n", " #   Column       Non-Null Count  Dtype  \n", "---  ------       --------------  -----  \n", " 0   Date         4592 non-null   object \n", " 1   City         4592 non-null   object \n", " 2   District     4592 non-null   object \n", " 3   Amount       4592 non-null   float64\n", " 4   Currency     4592 non-null   object \n", " 5   Property     4592 non-null   object \n", " 6   Seller       4592 non-null   object \n", " 7   Area         4592 non-null   float64\n", " 8   Rooms        4536 non-null   float64\n", " 9   Bathrooms    2238 non-null   float64\n", " 10  Parking      4592 non-null   object \n", " 11  Garden       4592 non-null   bool   \n", " 12  Balcony      4592 non-null   bool   \n", " 13  Terrace      4592 non-null   bool   \n", " 14  Floor        4592 non-null   bool   \n", " 15  New          4592 non-null   bool   \n", " 16  Estate       4592 non-null   bool   \n", " 17  Townhouse    4592 non-null   bool   \n", " 18  Apartment    4592 non-null   bool   \n", " 19  Land         4592 non-null   bool   \n", " 20  Studio       4592 non-null   bool   \n", " 21  Title        4592 non-null   object \n", " 22  Description  4592 non-null   object \n", " 23  Link         4592 non-null   object \n", "dtypes: bool(10), float64(4), object(10)\n", "memory usage: 547.2+ KB\n"]}], "source": ["data.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Impute missing values"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The next step is to fill in missing values for numeric columns `Amount` `Area` `Rooms` and `Bathrooms`. We use the `KNNImputer` to accomplish this."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["numeric = list(data.select_dtypes('number').columns)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["mask = (data['Bathrooms'].isna() == True | data['Rooms'].isna())\n", "missing = data[numeric]\n", "\n", "imputer = KNNImputer(n_neighbors=5)\n", "imputer.fit(missing)\n", "\n", "missing = imputer.transform(missing)\n", "missing = pd.DataFrame(missing, columns=numeric)\n", "\n", "for col in numeric:\n", "    data[col] = missing[col]\n", "    \n", "for col in numeric:\n", "    data[col] = data[col].apply(lambda x: round(x))    "]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(4592, 24)\n"]}], "source": ["print(data.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Verify that there are no `NaN`s in data."]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["data.isnull().sum().sum()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Remove columns that will not be used further."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["data = data.drop(['Title', \n", "                  'Description', \n", "                  '<PERSON>', \n", "                  'Property', \n", "                  'City', \n", "                  'Currency', \n", "                  'Date'], axis=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Take a last peek at the data."]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>District</th>\n", "      <th>Amount</th>\n", "      <th>Seller</th>\n", "      <th>Area</th>\n", "      <th>Rooms</th>\n", "      <th>Bathrooms</th>\n", "      <th>Parking</th>\n", "      <th>Garden</th>\n", "      <th>Balcony</th>\n", "      <th>Terrace</th>\n", "      <th>Floor</th>\n", "      <th>New</th>\n", "      <th>Estate</th>\n", "      <th>Townhouse</th>\n", "      <th>Apartment</th>\n", "      <th>Land</th>\n", "      <th>Studio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>595000</td>\n", "      <td>realtor</td>\n", "      <td>78</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>podgorze</td>\n", "      <td>449000</td>\n", "      <td>realtor</td>\n", "      <td>61</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>nowa huta</td>\n", "      <td>449000</td>\n", "      <td>realtor</td>\n", "      <td>58</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>595000</td>\n", "      <td>realtor</td>\n", "      <td>78</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>no parking</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>k<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>430000</td>\n", "      <td>realtor</td>\n", "      <td>48</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>garage</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    District  Amount   Seller  Area  Rooms  Bathrooms     Parking  Garden  \\\n", "0  krowodrza  595000  realtor    78      4          2  no parking   False   \n", "1   podgorze  449000  realtor    61      3          1  no parking   False   \n", "2  nowa huta  449000  realtor    58      3          1  no parking   False   \n", "3  krowodrza  595000  realtor    78      4          2  no parking   False   \n", "4  krowodrza  430000  realtor    48      2          1      garage   False   \n", "\n", "   Balcony  Terrace  Floor    New  Estate  Townhouse  Apartment   Land  Studio  \n", "0     True    False  False  False   False      False      False  False   False  \n", "1     True    False   True  False   False      False      False  False   False  \n", "2     True    False  False   True   False      False      False  False   False  \n", "3     True    False  False  False   False      False      False  False   False  \n", "4     True    False   True  False    True      False      False  False   False  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Amount</th>\n", "      <th>Area</th>\n", "      <th>Rooms</th>\n", "      <th>Bathrooms</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>4,592.00</td>\n", "      <td>4,592.00</td>\n", "      <td>4,592.00</td>\n", "      <td>4,592.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>535,522.19</td>\n", "      <td>55.93</td>\n", "      <td>2.61</td>\n", "      <td>1.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>222,331.92</td>\n", "      <td>20.25</td>\n", "      <td>0.99</td>\n", "      <td>0.33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>214,000.00</td>\n", "      <td>22.00</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>390,000.00</td>\n", "      <td>41.00</td>\n", "      <td>2.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>470,000.00</td>\n", "      <td>53.00</td>\n", "      <td>3.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>618,775.00</td>\n", "      <td>66.00</td>\n", "      <td>3.00</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1,525,000.00</td>\n", "      <td>135.00</td>\n", "      <td>6.00</td>\n", "      <td>4.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Amount     Area    Rooms  Bathrooms\n", "count     4,592.00 4,592.00 4,592.00   4,592.00\n", "mean    535,522.19    55.93     2.61       1.10\n", "std     222,331.92    20.25     0.99       0.33\n", "min     214,000.00    22.00     1.00       1.00\n", "25%     390,000.00    41.00     2.00       1.00\n", "50%     470,000.00    53.00     3.00       1.00\n", "75%     618,775.00    66.00     3.00       1.00\n", "max   1,525,000.00   135.00     6.00       4.00"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Save it for further analysis."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["data.to_csv('../flats-data/cleaned_data.csv', index=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.2"}}, "nbformat": 4, "nbformat_minor": 4}